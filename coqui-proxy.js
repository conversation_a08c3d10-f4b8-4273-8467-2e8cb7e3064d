const http = require('http');
const https = require('https');
const url = require('url');

const PORT = 3001;
const COQUI_HOST = 'localhost';
const COQUI_PORT = 5002;

const server = http.createServer((req, res) => {
  // Enable CORS
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

  // Handle preflight requests
  if (req.method === 'OPTIONS') {
    res.writeHead(200);
    res.end();
    return;
  }

  // Parse the request URL
  const parsedUrl = url.parse(req.url, true);
  
  // Only proxy /api/tts requests
  if (parsedUrl.pathname === '/api/tts') {
    // Forward the request to Coqui TTS server
    const options = {
      hostname: COQUI_HOST,
      port: COQUI_PORT,
      path: req.url,
      method: req.method,
      headers: {
        ...req.headers,
        host: `${COQUI_HOST}:${COQUI_PORT}`
      }
    };

    const proxyReq = http.request(options, (proxyRes) => {
      // Copy status code and headers
      res.writeHead(proxyRes.statusCode, proxyRes.headers);
      
      // Pipe the response
      proxyRes.pipe(res);
    });

    proxyReq.on('error', (err) => {
      console.error('Proxy request error:', err);
      res.writeHead(500);
      res.end('Proxy error');
    });

    // Pipe the request body
    req.pipe(proxyReq);
  } else {
    // Return 404 for other paths
    res.writeHead(404);
    res.end('Not found');
  }
});

server.listen(PORT, () => {
  console.log(`🚀 Coqui TTS Proxy server running on http://localhost:${PORT}`);
  console.log(`📡 Proxying requests to Coqui TTS server at http://${COQUI_HOST}:${COQUI_PORT}`);
  console.log('✅ CORS enabled for all origins');
});
