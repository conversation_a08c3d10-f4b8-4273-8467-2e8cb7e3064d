import { NextRequest, NextResponse } from 'next/server'
import { coquiTTSService, coquiUtils } from '@/lib/coqui'
import { localStorageService } from '@/lib/localStorage'

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData()
    const name = formData.get('name') as string
    const description = formData.get('description') as string
    const audioFile = formData.get('audio') as File

    if (!name || !audioFile) {
      return NextResponse.json(
        { error: 'Name and audio file are required' },
        { status: 400 }
      )
    }

    // Validate audio file for Coqui
    const validation = coquiUtils.validateAudioForCoqui(audioFile)
    if (!validation.valid) {
      return NextResponse.json(
        { error: validation.error },
        { status: 400 }
      )
    }

    console.log('Creating Coqui voice clone:', {
      name,
      description,
      audioFileName: audioFile.name,
      audioFileType: audioFile.type,
      audioFileSize: audioFile.size
    })

    // Check if Coqui TTS server is running
    const isServerRunning = await coquiTTSService.checkServerStatus()
    
    if (!isServerRunning) {
      // Return setup instructions if server is not running
      return NextResponse.json({
        error: 'Coqui TTS server not running',
        setupInstructions: coquiTTSService.getSetupInstructions(),
        fallbackAvailable: true
      }, { status: 503 })
    }

    try {
      // Clone voice using Coqui TTS
      const voiceModel = await coquiTTSService.cloneVoice(
        name,
        description || `Coqui voice clone: ${name}`,
        audioFile
      )

      console.log('Coqui voice clone created:', voiceModel)

      return NextResponse.json({
        success: true,
        voiceModel: {
          ...voiceModel,
          type: 'coqui'
        },
        message: 'Voice clone created successfully with Coqui TTS!'
      })

    } catch (coquiError) {
      console.error('Coqui TTS error:', coquiError)
      
      // Create a fallback voice model for demo purposes
      const fallbackModel = {
        id: `coqui_demo_${Date.now()}`,
        name,
        description: description || `Demo Coqui voice: ${name}`,
        voice_id: `coqui_${Date.now()}`,
        model_path: 'tts_models/multilingual/multi-dataset/xtts_v2',
        speaker_wav_path: '', // Would store the uploaded audio path
        language: 'en',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        type: 'coqui_demo'
      }

      return NextResponse.json({
        success: true,
        voiceModel: fallbackModel,
        message: 'Demo voice model created. Start Coqui TTS server for full functionality.',
        isDemo: true
      })
    }

  } catch (error) {
    console.error('Coqui voice cloning error:', error)
    return NextResponse.json(
      { error: 'Failed to create Coqui voice clone. Please try again.' },
      { status: 500 }
    )
  }
}

export async function GET() {
  try {
    // Check server status
    const isServerRunning = await coquiTTSService.checkServerStatus()
    
    let voiceModels = []
    let serverInfo = null

    if (isServerRunning) {
      // Get server info and available models
      serverInfo = await coquiTTSService.getServerInfo()
    }

    // Get voice models from localStorage (Coqui voices are stored locally)
    const localModels = typeof window !== 'undefined' ? 
      JSON.parse(localStorage.getItem('voice_clone_models') || '[]') : []
    
    // Filter for Coqui models
    voiceModels = localModels.filter((model: any) => 
      model.type === 'coqui' || model.type === 'coqui_demo'
    )

    return NextResponse.json({
      success: true,
      voiceModels,
      serverRunning: isServerRunning,
      serverInfo,
      setupInstructions: isServerRunning ? null : coquiTTSService.getSetupInstructions()
    })

  } catch (error) {
    console.error('Error fetching Coqui voice models:', error)
    return NextResponse.json({
      success: true,
      voiceModels: [],
      serverRunning: false,
      setupInstructions: coquiTTSService.getSetupInstructions()
    })
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const voiceId = searchParams.get('voiceId')

    if (!voiceId) {
      return NextResponse.json(
        { error: 'Voice ID is required' },
        { status: 400 }
      )
    }

    // For Coqui voices, we remove from localStorage
    // In a full implementation, you'd also clean up model files

    return NextResponse.json({
      success: true,
      message: 'Coqui voice model deleted successfully'
    })

  } catch (error) {
    console.error('Coqui voice deletion error:', error)
    return NextResponse.json(
      { error: 'Failed to delete Coqui voice model' },
      { status: 500 }
    )
  }
}
