// Local storage utilities for voice models
// This serves as a fallback when Supabase is not available

export interface LocalVoiceModel {
  id: string
  name: string
  description?: string
  voice_id: string
  created_at: string
}

const VOICE_MODELS_KEY = 'voice_clone_models'

export const localStorageService = {
  // Get all voice models from localStorage
  getVoiceModels(): LocalVoiceModel[] {
    if (typeof window === 'undefined') return []
    
    try {
      const stored = localStorage.getItem(VOICE_MODELS_KEY)
      return stored ? JSON.parse(stored) : []
    } catch (error) {
      console.error('Error reading voice models from localStorage:', error)
      return []
    }
  },

  // Save a voice model to localStorage
  saveVoiceModel(voiceModel: LocalVoiceModel): void {
    if (typeof window === 'undefined') return
    
    try {
      const existing = this.getVoiceModels()
      const updated = [...existing, voiceModel]
      localStorage.setItem(VOICE_MODELS_KEY, JSON.stringify(updated))
    } catch (error) {
      console.error('Error saving voice model to localStorage:', error)
    }
  },

  // Remove a voice model from localStorage
  removeVoiceModel(voiceId: string): void {
    if (typeof window === 'undefined') return
    
    try {
      const existing = this.getVoiceModels()
      const filtered = existing.filter(model => model.voice_id !== voiceId)
      localStorage.setItem(VOICE_MODELS_KEY, JSON.stringify(filtered))
    } catch (error) {
      console.error('Error removing voice model from localStorage:', error)
    }
  },

  // Clear all voice models
  clearVoiceModels(): void {
    if (typeof window === 'undefined') return
    
    try {
      localStorage.removeItem(VOICE_MODELS_KEY)
    } catch (error) {
      console.error('Error clearing voice models from localStorage:', error)
    }
  }
}
