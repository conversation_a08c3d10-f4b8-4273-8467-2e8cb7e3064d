import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// Database types
export interface VoiceModel {
  id: string
  name: string
  description?: string
  voice_id: string
  audio_url: string
  created_at: string
  updated_at: string
}

export interface GeneratedAudio {
  id: string
  voice_model_id: string
  text: string
  audio_url: string
  created_at: string
}

// Database operations
export const voiceModelService = {
  async create(voiceModel: Omit<VoiceModel, 'id' | 'created_at' | 'updated_at'>) {
    const { data, error } = await supabase
      .from('voice_models')
      .insert(voiceModel)
      .select()
      .single()
    
    if (error) throw error
    return data
  },

  async getAll() {
    const { data, error } = await supabase
      .from('voice_models')
      .select('*')
      .order('created_at', { ascending: false })
    
    if (error) throw error
    return data
  },

  async getById(id: string) {
    const { data, error } = await supabase
      .from('voice_models')
      .select('*')
      .eq('id', id)
      .single()
    
    if (error) throw error
    return data
  },

  async delete(id: string) {
    const { error } = await supabase
      .from('voice_models')
      .delete()
      .eq('id', id)
    
    if (error) throw error
  }
}

export const generatedAudioService = {
  async create(audio: Omit<GeneratedAudio, 'id' | 'created_at'>) {
    const { data, error } = await supabase
      .from('generated_audio')
      .insert(audio)
      .select()
      .single()
    
    if (error) throw error
    return data
  },

  async getByVoiceModel(voiceModelId: string) {
    const { data, error } = await supabase
      .from('generated_audio')
      .select('*')
      .eq('voice_model_id', voiceModelId)
      .order('created_at', { ascending: false })
    
    if (error) throw error
    return data
  }
}
