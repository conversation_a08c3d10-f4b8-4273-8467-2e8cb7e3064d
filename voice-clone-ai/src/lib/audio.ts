// Audio recording and processing utilities

export interface AudioRecorderOptions {
  mimeType?: string
  audioBitsPerSecond?: number
  sampleRate?: number
}

export class AudioRecorder {
  private mediaRecorder: MediaRecorder | null = null
  private audioChunks: Blob[] = []
  private stream: MediaStream | null = null
  private audioContext: AudioContext | null = null
  private analyser: AnalyserNode | null = null
  private dataArray: Uint8Array | null = null

  constructor(private options: AudioRecorderOptions = {}) {
    this.options = {
      mimeType: 'audio/webm;codecs=opus',
      audioBitsPerSecond: 128000,
      sampleRate: 44100,
      ...options,
    }
  }

  async initialize(): Promise<void> {
    try {
      this.stream = await navigator.mediaDevices.getUserMedia({
        audio: {
          sampleRate: this.options.sampleRate,
          channelCount: 1,
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true,
        },
      })

      // Set up audio analysis
      this.audioContext = new AudioContext()
      this.analyser = this.audioContext.createAnalyser()
      this.analyser.fftSize = 256
      
      const source = this.audioContext.createMediaStreamSource(this.stream)
      source.connect(this.analyser)
      
      this.dataArray = new Uint8Array(this.analyser.frequencyBinCount)

      // Set up MediaRecorder
      this.mediaRecorder = new MediaRecorder(this.stream, {
        mimeType: this.options.mimeType,
        audioBitsPerSecond: this.options.audioBitsPerSecond,
      })

      this.mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          this.audioChunks.push(event.data)
        }
      }
    } catch (error) {
      throw new Error(`Failed to initialize audio recorder: ${error}`)
    }
  }

  startRecording(): void {
    if (!this.mediaRecorder) {
      throw new Error('Audio recorder not initialized')
    }

    this.audioChunks = []
    this.mediaRecorder.start(100) // Collect data every 100ms
  }

  stopRecording(): Promise<Blob> {
    return new Promise((resolve, reject) => {
      if (!this.mediaRecorder) {
        reject(new Error('Audio recorder not initialized'))
        return
      }

      this.mediaRecorder.onstop = () => {
        const audioBlob = new Blob(this.audioChunks, { type: this.options.mimeType })
        resolve(audioBlob)
      }

      this.mediaRecorder.stop()
    })
  }

  getAudioLevel(): number {
    if (!this.analyser || !this.dataArray) return 0

    this.analyser.getByteFrequencyData(this.dataArray)
    
    let sum = 0
    for (let i = 0; i < this.dataArray.length; i++) {
      sum += this.dataArray[i]
    }
    
    return sum / this.dataArray.length / 255 // Normalize to 0-1
  }

  isRecording(): boolean {
    return this.mediaRecorder?.state === 'recording'
  }

  cleanup(): void {
    if (this.mediaRecorder && this.mediaRecorder.state !== 'inactive') {
      this.mediaRecorder.stop()
    }

    if (this.stream) {
      this.stream.getTracks().forEach(track => track.stop())
    }

    if (this.audioContext) {
      this.audioContext.close()
    }

    this.mediaRecorder = null
    this.stream = null
    this.audioContext = null
    this.analyser = null
    this.dataArray = null
  }
}

// Audio utility functions
export const audioUtils = {
  // Convert blob to base64
  blobToBase64(blob: Blob): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.onload = () => {
        const result = reader.result as string
        resolve(result.split(',')[1]) // Remove data:audio/webm;base64, prefix
      }
      reader.onerror = reject
      reader.readAsDataURL(blob)
    })
  },

  // Convert blob to array buffer
  blobToArrayBuffer(blob: Blob): Promise<ArrayBuffer> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.onload = () => resolve(reader.result as ArrayBuffer)
      reader.onerror = reject
      reader.readAsArrayBuffer(blob)
    })
  },

  // Create audio URL from blob
  createAudioURL(blob: Blob): string {
    return URL.createObjectURL(blob)
  },

  // Download audio file
  downloadAudio(blob: Blob, filename: string): void {
    const url = this.createAudioURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = filename
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  },

  // Format duration in seconds to MM:SS
  formatDuration(seconds: number): string {
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = Math.floor(seconds % 60)
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`
  },

  // Validate audio file
  validateAudioFile(file: File): { valid: boolean; error?: string } {
    const maxSize = 10 * 1024 * 1024 // 10MB
    const allowedTypes = ['audio/wav', 'audio/mp3', 'audio/mpeg', 'audio/webm', 'audio/ogg']

    if (file.size > maxSize) {
      return { valid: false, error: 'File size must be less than 10MB' }
    }

    if (!allowedTypes.includes(file.type)) {
      return { valid: false, error: 'File must be a valid audio format (WAV, MP3, WebM, OGG)' }
    }

    return { valid: true }
  },

  // Convert array buffer to blob
  arrayBufferToBlob(buffer: ArrayBuffer, mimeType: string = 'audio/mpeg'): Blob {
    return new Blob([buffer], { type: mimeType })
  },

  // Get audio duration
  getAudioDuration(file: File): Promise<number> {
    return new Promise((resolve, reject) => {
      const audio = new Audio()
      audio.onloadedmetadata = () => {
        resolve(audio.duration)
      }
      audio.onerror = reject
      audio.src = URL.createObjectURL(file)
    })
  }
}
