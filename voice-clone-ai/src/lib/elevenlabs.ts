// ElevenLabs API integration
const ELEVENLABS_API_KEY = process.env.NEXT_PUBLIC_ELEVENLABS_API_KEY
const ELEVENLABS_BASE_URL = 'https://api.elevenlabs.io/v1'

export interface ElevenLabsVoice {
  voice_id: string
  name: string
  samples: Array<{
    sample_id: string
    file_name: string
    mime_type: string
    size_bytes: number
    hash: string
  }>
  category: string
  fine_tuning: {
    is_allowed_to_fine_tune: boolean
    finetuning_state: string
    verification_failures: string[]
    verification_attempts_count: number
    manual_verification_requested: boolean
  }
  labels: Record<string, string>
  description: string
  preview_url: string
  available_for_tiers: string[]
  settings: {
    stability: number
    similarity_boost: number
    style: number
    use_speaker_boost: boolean
  }
  sharing: {
    status: string
    history_item_sample_id: string
    original_voice_id: string
    public_owner_id: string
    liked_by_count: number
    cloned_by_count: number
  }
  high_quality_base_model_ids: string[]
}

export class ElevenLabsService {
  private apiKey: string

  constructor(apiKey?: string) {
    this.apiKey = apiKey || ELEVENLABS_API_KEY || ''
    if (!this.apiKey) {
      throw new Error('ElevenLabs API key is required')
    }
  }

  private async makeRequest(endpoint: string, options: RequestInit = {}) {
    const url = `${ELEVENLABS_BASE_URL}${endpoint}`
    const response = await fetch(url, {
      ...options,
      headers: {
        'xi-api-key': this.apiKey,
        'Content-Type': 'application/json',
        ...options.headers,
      },
    })

    if (!response.ok) {
      const error = await response.text()
      throw new Error(`ElevenLabs API error: ${response.status} - ${error}`)
    }

    return response
  }

  async getVoices(): Promise<{ voices: ElevenLabsVoice[] }> {
    const response = await this.makeRequest('/voices')
    return response.json()
  }

  async cloneVoice(name: string, description: string, files: File[]): Promise<ElevenLabsVoice> {
    console.log('ElevenLabs cloneVoice called with:', {
      name,
      description,
      filesCount: files.length,
      files: files.map(f => ({ name: f.name, type: f.type, size: f.size }))
    })

    const formData = new FormData()
    formData.append('name', name)
    formData.append('description', description)

    files.forEach((file, index) => {
      // Ensure proper file extension
      let extension = file.name.split('.').pop() || 'webm'
      if (file.type.includes('webm')) extension = 'webm'
      else if (file.type.includes('wav')) extension = 'wav'
      else if (file.type.includes('mp3')) extension = 'mp3'
      else if (file.type.includes('ogg')) extension = 'ogg'

      const filename = `sample_${index}.${extension}`
      console.log(`Appending file ${index}:`, { originalName: file.name, newName: filename, type: file.type })
      formData.append('files', file, filename)
    })

    console.log('Making request to ElevenLabs API...')
    const response = await fetch(`${ELEVENLABS_BASE_URL}/voices/add`, {
      method: 'POST',
      headers: {
        'xi-api-key': this.apiKey,
      },
      body: formData,
    })

    console.log('ElevenLabs response status:', response.status)

    if (!response.ok) {
      const error = await response.text()
      console.error('ElevenLabs error response:', error)
      throw new Error(`Voice cloning failed: ${response.status} - ${error}`)
    }

    const result = await response.json()
    console.log('ElevenLabs success response:', result)
    return result
  }

  async generateSpeech(
    voiceId: string, 
    text: string, 
    options: {
      model_id?: string
      voice_settings?: {
        stability?: number
        similarity_boost?: number
        style?: number
        use_speaker_boost?: boolean
      }
    } = {}
  ): Promise<ArrayBuffer> {
    const body = {
      text,
      model_id: options.model_id || 'eleven_monolingual_v1',
      voice_settings: {
        stability: 0.5,
        similarity_boost: 0.5,
        style: 0.0,
        use_speaker_boost: true,
        ...options.voice_settings,
      },
    }

    const response = await fetch(`${ELEVENLABS_BASE_URL}/text-to-speech/${voiceId}`, {
      method: 'POST',
      headers: {
        'xi-api-key': this.apiKey,
        'Content-Type': 'application/json',
        'Accept': 'audio/mpeg',
      },
      body: JSON.stringify(body),
    })

    if (!response.ok) {
      const error = await response.text()
      throw new Error(`Speech generation failed: ${response.status} - ${error}`)
    }

    return response.arrayBuffer()
  }

  async deleteVoice(voiceId: string): Promise<void> {
    await this.makeRequest(`/voices/${voiceId}`, {
      method: 'DELETE',
    })
  }

  async getVoiceSettings(voiceId: string) {
    const response = await this.makeRequest(`/voices/${voiceId}/settings`)
    return response.json()
  }

  async updateVoiceSettings(voiceId: string, settings: {
    stability?: number
    similarity_boost?: number
    style?: number
    use_speaker_boost?: boolean
  }) {
    const response = await this.makeRequest(`/voices/${voiceId}/settings/edit`, {
      method: 'POST',
      body: JSON.stringify(settings),
    })
    return response.json()
  }
}

export const elevenLabsService = new ElevenLabsService()
