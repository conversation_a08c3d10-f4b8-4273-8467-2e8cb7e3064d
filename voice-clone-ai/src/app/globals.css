@import "tailwindcss";

@layer base {
  * {
    @apply border-gray-800;
  }
  body {
    @apply bg-black text-white;
  }
}

@layer components {
  .btn-primary {
    @apply bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-200 transform hover:scale-105 shadow-lg hover:shadow-xl;
  }

  .btn-secondary {
    @apply bg-gray-800 hover:bg-gray-700 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-200 border border-gray-600 hover:border-gray-500;
  }

  .card {
    @apply bg-gray-900/50 backdrop-blur-sm border border-gray-800 rounded-xl p-6 shadow-xl;
  }

  .input-field {
    @apply bg-gray-900/50 border border-gray-700 rounded-lg px-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200;
  }

  .gradient-text {
    @apply bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-transparent;
  }

  .audio-visualizer {
    @apply flex items-end justify-center space-x-1 h-16;
  }

  .audio-bar {
    @apply bg-gradient-to-t from-purple-600 to-blue-400 rounded-full transition-all duration-100 ease-out;
  }
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }

  .animate-pulse-slow {
    animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  .animate-bounce-slow {
    animation: bounce 2s infinite;
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  @apply bg-gray-900;
}

::-webkit-scrollbar-thumb {
  @apply bg-gray-700 rounded-full;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-gray-600;
}

/* Audio waveform animation */
@keyframes waveform {
  0%, 100% { height: 4px; }
  50% { height: 32px; }
}

.waveform-bar {
  animation: waveform 1s ease-in-out infinite;
}

.waveform-bar:nth-child(2) { animation-delay: 0.1s; }
.waveform-bar:nth-child(3) { animation-delay: 0.2s; }
.waveform-bar:nth-child(4) { animation-delay: 0.3s; }
.waveform-bar:nth-child(5) { animation-delay: 0.4s; }
