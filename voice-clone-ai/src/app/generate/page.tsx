'use client'

import { useState, useEffect, useRef } from 'react'
import { motion } from 'framer-motion'
import { ArrowLeft, Sparkles, Play, Pause, Download, Settings, Volume2, Mic } from 'lucide-react'
import Link from 'next/link'
import { localStorageService } from '@/lib/localStorage'
// import CoquiSetup from '@/components/CoquiSetup'

interface VoiceModel {
  id: string
  name: string
  description?: string
  voice_id: string
  created_at: string
}

interface VoiceSettings {
  stability: number
  similarity_boost: number
  style: number
  use_speaker_boost: boolean
}

export default function GeneratePage() {
  const [voiceModels, setVoiceModels] = useState<VoiceModel[]>([])
  const [selectedVoice, setSelectedVoice] = useState<VoiceModel | null>(null)
  const [text, setText] = useState('')
  const [isGenerating, setIsGenerating] = useState(false)
  const [isPlaying, setIsPlaying] = useState(false)
  const [audioUrl, setAudioUrl] = useState<string | null>(null)
  const [showSettings, setShowSettings] = useState(false)
  const [settings, setSettings] = useState<VoiceSettings>({
    stability: 0.5,
    similarity_boost: 0.5,
    style: 0.0,
    use_speaker_boost: true
  })
  const [error, setError] = useState<string | null>(null)
  const [coquiServerReady, setCoquiServerReady] = useState(false)

  const audioRef = useRef<HTMLAudioElement | null>(null)

  useEffect(() => {
    fetchVoiceModels()
  }, [])

  const fetchVoiceModels = async () => {
    try {
      // Check if Coqui TTS server is running
      let serverRunning = false
      try {
        const response = await fetch('http://localhost:5002', { method: 'GET' })
        serverRunning = response.ok
        setCoquiServerReady(serverRunning)
      } catch (error) {
        console.log('Coqui TTS server not running')
        setCoquiServerReady(false)
      }

      // Get voice models from localStorage
      const localModels = localStorageService.getVoiceModels()
      let allVoiceModels: VoiceModel[] = []

      if (localModels.length > 0) {
        // Convert local models to the expected format
        allVoiceModels = localModels.map(model => ({
          ...model,
          updated_at: model.created_at
        }))
      } else {
        // Create a demo voice model if none exist
        allVoiceModels = [{
          id: 'demo-voice-1',
          name: 'Demo Voice (Coqui TTS)',
          description: 'Default Coqui TTS voice using Tacotron2',
          voice_id: 'coqui-demo',
          created_at: new Date().toISOString()
        }]
      }

      setVoiceModels(allVoiceModels)
      if (allVoiceModels.length > 0) {
        setSelectedVoice(allVoiceModels[0])
      }
    } catch (error) {
      console.error('Failed to fetch voice models:', error)
      setError('Failed to load voice models')
    }
  }

  const generateSpeech = async () => {
    if (!selectedVoice || !text.trim()) return

    setIsGenerating(true)
    setError(null)

    try {
      // Call Coqui TTS server directly
      const requestBody = {
        text: text.trim(),
        model_name: 'tts_models/en/ljspeech/tacotron2-DDC',
        language: 'en',
        speed: settings.stability || 1.0
      }

      console.log('Generating speech with Coqui TTS:', requestBody)

      const response = await fetch('http://localhost:5002/api/tts', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      })

      if (!response.ok) {
        const errorText = await response.text()
        throw new Error(`Coqui TTS failed: ${response.status} - ${errorText}`)
      }

      // Get audio blob from response
      const audioBlob = await response.blob()

      // Clean up previous audio URL
      if (audioUrl) {
        URL.revokeObjectURL(audioUrl)
      }

      // Create new audio URL
      const newAudioUrl = URL.createObjectURL(audioBlob)
      setAudioUrl(newAudioUrl)

      console.log('Speech generated successfully!')

    } catch (error) {
      console.error('Speech generation failed:', error)
      setError(error instanceof Error ? error.message : 'Failed to generate speech')
    } finally {
      setIsGenerating(false)
    }
  }

  const playAudio = () => {
    if (!audioRef.current || !audioUrl) return

    if (isPlaying) {
      audioRef.current.pause()
      setIsPlaying(false)
    } else {
      audioRef.current.play()
      setIsPlaying(true)
    }
  }

  const downloadAudio = () => {
    if (!audioUrl) return

    const a = document.createElement('a')
    a.href = audioUrl
    a.download = `voice-clone-${Date.now()}.mp3`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
  }

  const sampleTexts = [
    "Hello, this is my AI voice clone speaking. How do I sound?",
    "Welcome to the future of voice technology. Your voice, unlimited possibilities.",
    "I can read any text you give me in your own voice. Pretty amazing, right?",
    "The quick brown fox jumps over the lazy dog. This sentence contains every letter of the alphabet."
  ]

  return (
    <div className="min-h-screen py-8">
      {/* Navigation */}
      <nav className="fixed top-0 w-full z-50 bg-black/80 backdrop-blur-md border-b border-gray-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <Link href="/" className="flex items-center space-x-2 text-gray-300 hover:text-white transition-colors">
              <ArrowLeft className="w-5 h-5" />
              <span>Back to Home</span>
            </Link>
            <div className="flex items-center space-x-2">
              <Sparkles className="w-8 h-8 text-purple-400" />
              <span className="text-xl font-bold gradient-text">VoiceClone AI</span>
            </div>
          </div>
        </div>
      </nav>

      <div className="pt-24 px-4 sm:px-6 lg:px-8">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-12">
            <h1 className="text-4xl md:text-5xl font-bold mb-4">
              Generate <span className="gradient-text">Speech</span>
            </h1>
            <p className="text-xl text-gray-300 mb-4">
              Type any text and hear it spoken in your cloned voice using Coqui TTS
            </p>
            <div className="inline-flex items-center px-4 py-2 bg-blue-900/30 border border-blue-700 rounded-lg text-blue-300">
              <span className="text-sm">🚀 Powered by Coqui TTS - Free & Open Source</span>
            </div>
          </div>

          {error && (
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              className="bg-red-900/50 border border-red-700 rounded-lg p-4 text-red-200 mb-6 max-w-2xl mx-auto"
            >
              {error}
            </motion.div>
          )}

          {/* Coqui Setup Section */}
          {!coquiServerReady && (
            <div className="mb-8">
              <div className="card">
                <h3 className="text-xl font-semibold mb-4">Coqui TTS Server Required</h3>
                <p className="text-gray-300 mb-4">
                  To use voice cloning, you need to start the Coqui TTS server.
                  The server is currently {coquiServerReady ? 'running' : 'not running'}.
                </p>
                <button
                  onClick={() => {
                    setCoquiServerReady(true)
                    fetchVoiceModels()
                  }}
                  className="btn-primary"
                >
                  Mark Server as Ready
                </button>
              </div>
            </div>
          )}

          <div className="grid lg:grid-cols-3 gap-8">
            {/* Voice Selection */}
            <div className="lg:col-span-1">
              <div className="card">
                <h3 className="text-xl font-semibold mb-4 flex items-center">
                  <Mic className="w-5 h-5 mr-2 text-purple-400" />
                  Select Voice
                </h3>

                {voiceModels.length === 0 ? (
                  <div className="text-center py-8">
                    <p className="text-gray-400 mb-4">No voice models found</p>
                    <Link href="/record" className="btn-primary">
                      Create Voice Clone
                    </Link>
                  </div>
                ) : (
                  <div className="space-y-3">
                    {voiceModels.map((voice) => (
                      <button
                        key={voice.id}
                        onClick={() => setSelectedVoice(voice)}
                        className={`w-full p-4 rounded-lg border transition-all duration-200 text-left ${
                          selectedVoice?.id === voice.id
                            ? 'border-purple-500 bg-purple-900/20'
                            : 'border-gray-700 hover:border-gray-600'
                        }`}
                      >
                        <div className="font-medium">{voice.name}</div>
                        {voice.description && (
                          <div className="text-sm text-gray-400 mt-1">
                            {voice.description}
                          </div>
                        )}
                        <div className="text-xs text-gray-500 mt-2">
                          Created {new Date(voice.created_at).toLocaleDateString()}
                        </div>
                      </button>
                    ))}
                  </div>
                )}

                {/* Voice Settings */}
                <div className="mt-6">
                  <button
                    onClick={() => setShowSettings(!showSettings)}
                    className="btn-secondary w-full flex items-center justify-center space-x-2"
                  >
                    <Settings className="w-4 h-4" />
                    <span>Voice Settings</span>
                  </button>

                  {showSettings && (
                    <motion.div
                      initial={{ opacity: 0, height: 0 }}
                      animate={{ opacity: 1, height: 'auto' }}
                      className="mt-4 space-y-4"
                    >
                      <div>
                        <label className="block text-sm font-medium mb-2">
                          Stability: {settings.stability.toFixed(2)}
                        </label>
                        <input
                          type="range"
                          min="0"
                          max="1"
                          step="0.01"
                          value={settings.stability}
                          onChange={(e) => setSettings(prev => ({ ...prev, stability: parseFloat(e.target.value) }))}
                          className="w-full"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium mb-2">
                          Similarity: {settings.similarity_boost.toFixed(2)}
                        </label>
                        <input
                          type="range"
                          min="0"
                          max="1"
                          step="0.01"
                          value={settings.similarity_boost}
                          onChange={(e) => setSettings(prev => ({ ...prev, similarity_boost: parseFloat(e.target.value) }))}
                          className="w-full"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium mb-2">
                          Style: {settings.style.toFixed(2)}
                        </label>
                        <input
                          type="range"
                          min="0"
                          max="1"
                          step="0.01"
                          value={settings.style}
                          onChange={(e) => setSettings(prev => ({ ...prev, style: parseFloat(e.target.value) }))}
                          className="w-full"
                        />
                      </div>

                      <div className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          id="speaker-boost"
                          checked={settings.use_speaker_boost}
                          onChange={(e) => setSettings(prev => ({ ...prev, use_speaker_boost: e.target.checked }))}
                          className="rounded"
                        />
                        <label htmlFor="speaker-boost" className="text-sm">
                          Speaker Boost
                        </label>
                      </div>
                    </motion.div>
                  )}
                </div>
              </div>
            </div>

            {/* Text Input and Generation */}
            <div className="lg:col-span-2">
              <div className="card">
                <h3 className="text-xl font-semibold mb-4">Enter Text</h3>

                <textarea
                  value={text}
                  onChange={(e) => setText(e.target.value)}
                  placeholder="Type the text you want to convert to speech..."
                  className="input-field w-full h-40 resize-none mb-4"
                  maxLength={5000}
                />

                <div className="flex justify-between items-center mb-6">
                  <div className="text-sm text-gray-400">
                    {text.length}/5000 characters
                  </div>
                  <div className="text-sm text-gray-400">
                    Estimated: ~{Math.ceil(text.length / 10)} seconds
                  </div>
                </div>

                {/* Sample Texts */}
                <div className="mb-6">
                  <h4 className="text-sm font-medium mb-3">Quick Samples:</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                    {sampleTexts.map((sample, index) => (
                      <button
                        key={index}
                        onClick={() => setText(sample)}
                        className="text-left p-3 rounded-lg border border-gray-700 hover:border-gray-600 transition-colors text-sm"
                      >
                        "{sample.substring(0, 50)}..."
                      </button>
                    ))}
                  </div>
                </div>

                {/* Generate Button */}
                <button
                  onClick={generateSpeech}
                  disabled={!selectedVoice || !text.trim() || isGenerating || !coquiServerReady}
                  className="btn-primary w-full flex items-center justify-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isGenerating ? (
                    <>
                      <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white" />
                      <span>Generating with Coqui TTS...</span>
                    </>
                  ) : !coquiServerReady ? (
                    <>
                      <Volume2 className="w-5 h-5" />
                      <span>Start Coqui Server First</span>
                    </>
                  ) : (
                    <>
                      <Volume2 className="w-5 h-5" />
                      <span>Generate Speech</span>
                    </>
                  )}
                </button>

                {!coquiServerReady && (
                  <div className="text-sm text-yellow-400 text-center">
                    ⚠️ Coqui TTS server is required for speech generation
                  </div>
                )}

                {/* Audio Player */}
                {audioUrl && (
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="mt-6 p-4 bg-gray-800/50 rounded-lg"
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-4">
                        <button
                          onClick={playAudio}
                          className="btn-secondary flex items-center space-x-2"
                        >
                          {isPlaying ? (
                            <Pause className="w-4 h-4" />
                          ) : (
                            <Play className="w-4 h-4" />
                          )}
                          <span>{isPlaying ? 'Pause' : 'Play'}</span>
                        </button>

                        <div className="text-sm text-gray-400">
                          Generated speech ready
                        </div>
                      </div>

                      <button
                        onClick={downloadAudio}
                        className="btn-secondary flex items-center space-x-2"
                      >
                        <Download className="w-4 h-4" />
                        <span>Download</span>
                      </button>
                    </div>

                    <audio
                      ref={audioRef}
                      src={audioUrl}
                      onEnded={() => setIsPlaying(false)}
                      onPlay={() => setIsPlaying(true)}
                      onPause={() => setIsPlaying(false)}
                      className="hidden"
                    />
                  </motion.div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
