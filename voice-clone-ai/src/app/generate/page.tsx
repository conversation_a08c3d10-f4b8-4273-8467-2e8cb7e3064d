'use client'

import { useState, useEffect, useRef } from 'react'
import { motion } from 'framer-motion'
import { ArrowLeft, Sparkles, Play, Pause, Download, Settings, Volume2, Mic } from 'lucide-react'
import Link from 'next/link'
import { localStorageService } from '@/lib/localStorage'
import FreeSpeechGenerator from '@/components/FreeSpeechGenerator'

interface VoiceModel {
  id: string
  name: string
  description?: string
  voice_id: string
  created_at: string
}

interface VoiceSettings {
  stability: number
  similarity_boost: number
  style: number
  use_speaker_boost: boolean
}

export default function GeneratePage() {
  const [voiceModels, setVoiceModels] = useState<VoiceModel[]>([])
  const [selectedVoice, setSelectedVoice] = useState<VoiceModel | null>(null)
  const [text, setText] = useState('')
  const [isGenerating, setIsGenerating] = useState(false)
  const [isPlaying, setIsPlaying] = useState(false)
  const [audioUrl, setAudioUrl] = useState<string | null>(null)
  const [showSettings, setShowSettings] = useState(false)
  const [settings, setSettings] = useState<VoiceSettings>({
    stability: 0.5,
    similarity_boost: 0.5,
    style: 0.0,
    use_speaker_boost: true
  })
  const [error, setError] = useState<string | null>(null)

  const audioRef = useRef<HTMLAudioElement | null>(null)

  useEffect(() => {
    fetchVoiceModels()
  }, [])

  const fetchVoiceModels = async () => {
    try {
      // Try both free and paid API endpoints
      const [freeResponse, paidResponse] = await Promise.allSettled([
        fetch('/api/free-voice-clone'),
        fetch('/api/voice-clone')
      ])

      let allVoiceModels: VoiceModel[] = []

      // Get free voice models
      if (freeResponse.status === 'fulfilled') {
        const freeData = await freeResponse.value.json()
        if (freeData.success) {
          allVoiceModels = [...allVoiceModels, ...freeData.voiceModels]
        }
      }

      // Get paid voice models
      if (paidResponse.status === 'fulfilled') {
        const paidData = await paidResponse.value.json()
        if (paidData.success) {
          allVoiceModels = [...allVoiceModels, ...paidData.voiceModels]
        }
      }

      // Also check localStorage for voice models (fallback)
      const localModels = localStorageService.getVoiceModels()
      if (localModels.length > 0) {
        // Convert local models to the expected format
        const convertedLocalModels = localModels.map(model => ({
          ...model,
          updated_at: model.created_at
        }))

        // Merge with API models, avoiding duplicates
        const existingVoiceIds = allVoiceModels.map((m: VoiceModel) => m.voice_id)
        const newLocalModels = convertedLocalModels.filter(
          model => !existingVoiceIds.includes(model.voice_id)
        )

        allVoiceModels = [...allVoiceModels, ...newLocalModels]
      }

      setVoiceModels(allVoiceModels)
      if (allVoiceModels.length > 0) {
        setSelectedVoice(allVoiceModels[0])
      }
    } catch (error) {
      console.error('Failed to fetch voice models:', error)

      // Fallback to localStorage only
      const localModels = localStorageService.getVoiceModels()
      if (localModels.length > 0) {
        const convertedLocalModels = localModels.map(model => ({
          ...model,
          updated_at: model.created_at
        }))
        setVoiceModels(convertedLocalModels)
        setSelectedVoice(convertedLocalModels[0])
      } else {
        setError('Failed to load voice models')
      }
    }
  }

  const generateSpeech = async () => {
    if (!selectedVoice || !text.trim()) return

    setIsGenerating(true)
    setError(null)

    try {
      const response = await fetch('/api/generate-speech', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          text: text.trim(),
          voiceModelId: selectedVoice.id,
          settings
        }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to generate speech')
      }

      // Get audio blob from response
      const audioBlob = await response.blob()
      
      // Clean up previous audio URL
      if (audioUrl) {
        URL.revokeObjectURL(audioUrl)
      }

      // Create new audio URL
      const newAudioUrl = URL.createObjectURL(audioBlob)
      setAudioUrl(newAudioUrl)

    } catch (error) {
      console.error('Speech generation failed:', error)
      setError(error instanceof Error ? error.message : 'Failed to generate speech')
    } finally {
      setIsGenerating(false)
    }
  }

  const playAudio = () => {
    if (!audioRef.current || !audioUrl) return

    if (isPlaying) {
      audioRef.current.pause()
      setIsPlaying(false)
    } else {
      audioRef.current.play()
      setIsPlaying(true)
    }
  }

  const downloadAudio = () => {
    if (!audioUrl) return

    const a = document.createElement('a')
    a.href = audioUrl
    a.download = `voice-clone-${Date.now()}.mp3`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
  }

  const sampleTexts = [
    "Hello, this is my AI voice clone speaking. How do I sound?",
    "Welcome to the future of voice technology. Your voice, unlimited possibilities.",
    "I can read any text you give me in your own voice. Pretty amazing, right?",
    "The quick brown fox jumps over the lazy dog. This sentence contains every letter of the alphabet."
  ]

  return (
    <div className="min-h-screen py-8">
      {/* Navigation */}
      <nav className="fixed top-0 w-full z-50 bg-black/80 backdrop-blur-md border-b border-gray-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <Link href="/" className="flex items-center space-x-2 text-gray-300 hover:text-white transition-colors">
              <ArrowLeft className="w-5 h-5" />
              <span>Back to Home</span>
            </Link>
            <div className="flex items-center space-x-2">
              <Sparkles className="w-8 h-8 text-purple-400" />
              <span className="text-xl font-bold gradient-text">VoiceClone AI</span>
            </div>
          </div>
        </div>
      </nav>

      <div className="pt-24 px-4 sm:px-6 lg:px-8">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-12">
            <h1 className="text-4xl md:text-5xl font-bold mb-4">
              Generate <span className="gradient-text">Speech</span>
            </h1>
            <p className="text-xl text-gray-300 mb-4">
              Type any text and hear it spoken in your cloned voice
            </p>
            <div className="inline-flex items-center px-4 py-2 bg-green-900/30 border border-green-700 rounded-lg text-green-300">
              <span className="text-sm">🎉 100% Free - Uses Browser Speech Synthesis</span>
            </div>
          </div>

          {error && (
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              className="bg-red-900/50 border border-red-700 rounded-lg p-4 text-red-200 mb-6 max-w-2xl mx-auto"
            >
              {error}
            </motion.div>
          )}

          <div className="grid lg:grid-cols-3 gap-8">
            {/* Voice Selection */}
            <div className="lg:col-span-1">
              <div className="card">
                <h3 className="text-xl font-semibold mb-4 flex items-center">
                  <Mic className="w-5 h-5 mr-2 text-purple-400" />
                  Select Voice
                </h3>

                {voiceModels.length === 0 ? (
                  <div className="text-center py-8">
                    <p className="text-gray-400 mb-4">No voice models found</p>
                    <Link href="/record" className="btn-primary">
                      Create Voice Clone
                    </Link>
                  </div>
                ) : (
                  <div className="space-y-3">
                    {voiceModels.map((voice) => (
                      <button
                        key={voice.id}
                        onClick={() => setSelectedVoice(voice)}
                        className={`w-full p-4 rounded-lg border transition-all duration-200 text-left ${
                          selectedVoice?.id === voice.id
                            ? 'border-purple-500 bg-purple-900/20'
                            : 'border-gray-700 hover:border-gray-600'
                        }`}
                      >
                        <div className="font-medium">{voice.name}</div>
                        {voice.description && (
                          <div className="text-sm text-gray-400 mt-1">
                            {voice.description}
                          </div>
                        )}
                        <div className="text-xs text-gray-500 mt-2">
                          Created {new Date(voice.created_at).toLocaleDateString()}
                        </div>
                      </button>
                    ))}
                  </div>
                )}

                {/* Voice Settings */}
                <div className="mt-6">
                  <button
                    onClick={() => setShowSettings(!showSettings)}
                    className="btn-secondary w-full flex items-center justify-center space-x-2"
                  >
                    <Settings className="w-4 h-4" />
                    <span>Voice Settings</span>
                  </button>

                  {showSettings && (
                    <motion.div
                      initial={{ opacity: 0, height: 0 }}
                      animate={{ opacity: 1, height: 'auto' }}
                      className="mt-4 space-y-4"
                    >
                      <div>
                        <label className="block text-sm font-medium mb-2">
                          Stability: {settings.stability.toFixed(2)}
                        </label>
                        <input
                          type="range"
                          min="0"
                          max="1"
                          step="0.01"
                          value={settings.stability}
                          onChange={(e) => setSettings(prev => ({ ...prev, stability: parseFloat(e.target.value) }))}
                          className="w-full"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium mb-2">
                          Similarity: {settings.similarity_boost.toFixed(2)}
                        </label>
                        <input
                          type="range"
                          min="0"
                          max="1"
                          step="0.01"
                          value={settings.similarity_boost}
                          onChange={(e) => setSettings(prev => ({ ...prev, similarity_boost: parseFloat(e.target.value) }))}
                          className="w-full"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium mb-2">
                          Style: {settings.style.toFixed(2)}
                        </label>
                        <input
                          type="range"
                          min="0"
                          max="1"
                          step="0.01"
                          value={settings.style}
                          onChange={(e) => setSettings(prev => ({ ...prev, style: parseFloat(e.target.value) }))}
                          className="w-full"
                        />
                      </div>

                      <div className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          id="speaker-boost"
                          checked={settings.use_speaker_boost}
                          onChange={(e) => setSettings(prev => ({ ...prev, use_speaker_boost: e.target.checked }))}
                          className="rounded"
                        />
                        <label htmlFor="speaker-boost" className="text-sm">
                          Speaker Boost
                        </label>
                      </div>
                    </motion.div>
                  )}
                </div>
              </div>
            </div>

            {/* Text Input and Generation */}
            <div className="lg:col-span-2">
              <div className="card">
                <h3 className="text-xl font-semibold mb-4">Enter Text</h3>

                <textarea
                  value={text}
                  onChange={(e) => setText(e.target.value)}
                  placeholder="Type the text you want to convert to speech..."
                  className="input-field w-full h-40 resize-none mb-4"
                  maxLength={5000}
                />

                <div className="flex justify-between items-center mb-6">
                  <div className="text-sm text-gray-400">
                    {text.length}/5000 characters
                  </div>
                  <div className="text-sm text-gray-400">
                    Estimated: ~{Math.ceil(text.length / 10)} seconds
                  </div>
                </div>

                {/* Sample Texts */}
                <div className="mb-6">
                  <h4 className="text-sm font-medium mb-3">Quick Samples:</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                    {sampleTexts.map((sample, index) => (
                      <button
                        key={index}
                        onClick={() => setText(sample)}
                        className="text-left p-3 rounded-lg border border-gray-700 hover:border-gray-600 transition-colors text-sm"
                      >
                        "{sample.substring(0, 50)}..."
                      </button>
                    ))}
                  </div>
                </div>

                {/* Free Speech Generator */}
                {selectedVoice && (
                  <FreeSpeechGenerator
                    text={text}
                    voiceSettings={{
                      rate: settings.stability, // Map stability to rate
                      pitch: 1.0 + (settings.similarity_boost - 0.5), // Map similarity to pitch
                      volume: 0.8,
                      voiceName: selectedVoice.name
                    }}
                    onAudioGenerated={(audioBlob) => {
                      // Clean up previous audio URL
                      if (audioUrl) {
                        URL.revokeObjectURL(audioUrl)
                      }
                      const newAudioUrl = URL.createObjectURL(audioBlob)
                      setAudioUrl(newAudioUrl)
                    }}
                  />
                )}

                {/* Audio Player */}
                {audioUrl && (
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="mt-6 p-4 bg-gray-800/50 rounded-lg"
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-4">
                        <button
                          onClick={playAudio}
                          className="btn-secondary flex items-center space-x-2"
                        >
                          {isPlaying ? (
                            <Pause className="w-4 h-4" />
                          ) : (
                            <Play className="w-4 h-4" />
                          )}
                          <span>{isPlaying ? 'Pause' : 'Play'}</span>
                        </button>

                        <div className="text-sm text-gray-400">
                          Generated speech ready
                        </div>
                      </div>

                      <button
                        onClick={downloadAudio}
                        className="btn-secondary flex items-center space-x-2"
                      >
                        <Download className="w-4 h-4" />
                        <span>Download</span>
                      </button>
                    </div>

                    <audio
                      ref={audioRef}
                      src={audioUrl}
                      onEnded={() => setIsPlaying(false)}
                      onPlay={() => setIsPlaying(true)}
                      onPause={() => setIsPlaying(false)}
                      className="hidden"
                    />
                  </motion.div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
