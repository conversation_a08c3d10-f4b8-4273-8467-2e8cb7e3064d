'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { Mic, AudioWaveform, Sparkles, Play, Download, Upload, Volume2 } from 'lucide-react'
import Link from 'next/link'

export default function Home() {
  const [isLoaded, setIsLoaded] = useState(false)

  useEffect(() => {
    setIsLoaded(true)
  }, [])

  const features = [
    {
      icon: <Mic className="w-8 h-8" />,
      title: "Record Your Voice",
      description: "Just 30 seconds of your voice is all we need to create a perfect clone"
    },
    {
      icon: <AudioWaveform className="w-8 h-8" />,
      title: "AI Processing",
      description: "Our advanced AI analyzes your voice patterns and creates a digital twin"
    },
    {
      icon: <Volume2 className="w-8 h-8" />,
      title: "Generate Speech",
      description: "Type any text and hear it spoken in your own voice with perfect clarity"
    }
  ]

  const demoTexts = [
    "Hello, this is my voice clone speaking!",
    "I can say anything you want me to say.",
    "The future of voice technology is here.",
    "Your voice, unlimited possibilities."
  ]

  return (
    <div className="min-h-screen">
      {/* Navigation */}
      <nav className="fixed top-0 w-full z-50 bg-black/80 backdrop-blur-md border-b border-gray-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-2">
              <Sparkles className="w-8 h-8 text-purple-400" />
              <span className="text-xl font-bold gradient-text">VoiceClone AI</span>
            </div>
            <div className="hidden md:flex items-center space-x-8">
              <Link href="#features" className="text-gray-300 hover:text-white transition-colors">
                Features
              </Link>
              <Link href="#demo" className="text-gray-300 hover:text-white transition-colors">
                Demo
              </Link>
              <Link href="#pricing" className="text-gray-300 hover:text-white transition-colors">
                Pricing
              </Link>
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="pt-32 pb-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: isLoaded ? 1 : 0, y: isLoaded ? 0 : 20 }}
            transition={{ duration: 0.8 }}
          >
            <h1 className="text-5xl md:text-7xl font-bold mb-6">
              Clone Your Voice with{' '}
              <span className="gradient-text">AI Magic</span>
            </h1>
            <p className="text-xl md:text-2xl text-gray-300 mb-8 max-w-3xl mx-auto">
              Transform any text into speech using your own voice.
              Create podcasts, audiobooks, and content that sounds exactly like you.
            </p>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: isLoaded ? 1 : 0, y: isLoaded ? 0 : 20 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="flex flex-col sm:flex-row gap-4 justify-center mb-16"
          >
            <Link href="/record" className="btn-primary text-lg px-8 py-4">
              <Mic className="w-5 h-5 mr-2" />
              Start Cloning Now
            </Link>
            <Link href="#demo" className="btn-secondary text-lg px-8 py-4">
              <Play className="w-5 h-5 mr-2" />
              Watch Demo
            </Link>
          </motion.div>

          {/* Audio Visualizer Animation */}
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: isLoaded ? 1 : 0, scale: isLoaded ? 1 : 0.8 }}
            transition={{ duration: 1, delay: 0.4 }}
            className="audio-visualizer mb-20"
          >
            {[...Array(20)].map((_, i) => (
              <div
                key={i}
                className="waveform-bar w-2 bg-gradient-to-t from-purple-600 to-blue-400 rounded-full"
                style={{
                  animationDelay: `${i * 0.1}s`,
                  height: `${Math.random() * 40 + 10}px`
                }}
              />
            ))}
          </motion.div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold mb-4">
              How It <span className="gradient-text">Works</span>
            </h2>
            <p className="text-xl text-gray-300 max-w-2xl mx-auto">
              Three simple steps to create your perfect voice clone
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.2 }}
                className="card text-center"
              >
                <div className="text-purple-400 mb-4 flex justify-center">
                  {feature.icon}
                </div>
                <h3 className="text-xl font-semibold mb-3">{feature.title}</h3>
                <p className="text-gray-300">{feature.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Demo Section */}
      <section id="demo" className="py-20 px-4 sm:px-6 lg:px-8 bg-gray-900/30">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-4xl md:text-5xl font-bold mb-8">
            Experience the <span className="gradient-text">Magic</span>
          </h2>
          <p className="text-xl text-gray-300 mb-12">
            Listen to these examples of AI-generated voices
          </p>

          <div className="grid md:grid-cols-2 gap-6">
            {demoTexts.map((text, index) => (
              <div key={index} className="card">
                <p className="text-gray-300 mb-4">"{text}"</p>
                <button className="btn-secondary w-full">
                  <Play className="w-4 h-4 mr-2" />
                  Play Demo
                </button>
              </div>
            ))}
          </div>

          <div className="mt-12">
            <Link href="/record" className="btn-primary text-lg px-8 py-4">
              Create Your Voice Clone
            </Link>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-4xl md:text-5xl font-bold mb-6">
            Ready to Clone Your Voice?
          </h2>
          <p className="text-xl text-gray-300 mb-8">
            Join thousands of creators who are already using AI voice cloning
          </p>
          <Link href="/record" className="btn-primary text-lg px-8 py-4">
            <Mic className="w-5 h-5 mr-2" />
            Get Started Free
          </Link>
        </div>
      </section>

      {/* Footer */}
      <footer className="border-t border-gray-800 py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="flex items-center space-x-2 mb-4 md:mb-0">
              <Sparkles className="w-6 h-6 text-purple-400" />
              <span className="text-lg font-bold gradient-text">VoiceClone AI</span>
            </div>
            <div className="text-gray-400 text-sm">
              © 2024 VoiceClone AI. All rights reserved.
            </div>
          </div>
        </div>
      </footer>
    </div>
  )
}
