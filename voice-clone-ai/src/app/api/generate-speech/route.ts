import { NextRequest, NextResponse } from 'next/server'
import { elevenLabsService } from '@/lib/elevenlabs'
import { voiceModelService, generatedAudioService } from '@/lib/supabase'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { text, voiceModelId, settings } = body

    if (!text || !voiceModelId) {
      return NextResponse.json(
        { error: 'Text and voice model ID are required' },
        { status: 400 }
      )
    }

    // Validate text length
    if (text.length > 5000) {
      return NextResponse.json(
        { error: 'Text must be less than 5000 characters' },
        { status: 400 }
      )
    }

    // Get voice model from database
    const voiceModel = await voiceModelService.getById(voiceModelId)
    if (!voiceModel) {
      return NextResponse.json(
        { error: 'Voice model not found' },
        { status: 404 }
      )
    }

    // Generate speech using ElevenLabs
    console.log('Generating speech with ElevenLabs...')
    const audioBuffer = await elevenLabsService.generateSpeech(
      voiceModel.voice_id,
      text,
      {
        voice_settings: {
          stability: settings?.stability || 0.5,
          similarity_boost: settings?.similarity_boost || 0.5,
          style: settings?.style || 0.0,
          use_speaker_boost: settings?.use_speaker_boost || true,
        }
      }
    )

    // Convert ArrayBuffer to base64 for storage/transmission
    const audioBase64 = Buffer.from(audioBuffer).toString('base64')
    const audioDataUrl = `data:audio/mpeg;base64,${audioBase64}`

    // Store generated audio in database
    try {
      await generatedAudioService.create({
        voice_model_id: voiceModelId,
        text,
        audio_url: audioDataUrl, // In production, you'd store this in cloud storage
      })
    } catch (dbError) {
      console.warn('Failed to store generated audio in database:', dbError)
      // Continue anyway, as the main functionality still works
    }

    // Return audio data
    return new NextResponse(audioBuffer, {
      status: 200,
      headers: {
        'Content-Type': 'audio/mpeg',
        'Content-Length': audioBuffer.byteLength.toString(),
        'Cache-Control': 'public, max-age=31536000', // Cache for 1 year
      },
    })

  } catch (error) {
    console.error('Speech generation error:', error)
    
    // Handle specific ElevenLabs errors
    if (error instanceof Error) {
      if (error.message.includes('quota')) {
        return NextResponse.json(
          { error: 'API quota exceeded. Please try again later.' },
          { status: 429 }
        )
      }
      if (error.message.includes('unauthorized')) {
        return NextResponse.json(
          { error: 'Invalid API key configuration' },
          { status: 401 }
        )
      }
      if (error.message.includes('voice not found')) {
        return NextResponse.json(
          { error: 'Voice model not found or has been deleted' },
          { status: 404 }
        )
      }
    }

    return NextResponse.json(
      { error: 'Failed to generate speech. Please try again.' },
      { status: 500 }
    )
  }
}

// Get generated audio history for a voice model
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const voiceModelId = searchParams.get('voiceModelId')

    if (!voiceModelId) {
      return NextResponse.json(
        { error: 'Voice model ID is required' },
        { status: 400 }
      )
    }

    // Get generated audio history
    const generatedAudio = await generatedAudioService.getByVoiceModel(voiceModelId)
    
    return NextResponse.json({
      success: true,
      generatedAudio
    })

  } catch (error) {
    console.error('Error fetching generated audio:', error)
    return NextResponse.json(
      { error: 'Failed to fetch generated audio' },
      { status: 500 }
    )
  }
}
