import { NextRequest, NextResponse } from 'next/server'
import { elevenLabsService } from '@/lib/elevenlabs'
import { voiceModelService } from '@/lib/supabase'

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData()
    const name = formData.get('name') as string
    const description = formData.get('description') as string
    const audioFile = formData.get('audio') as File

    if (!name || !audioFile) {
      return NextResponse.json(
        { error: 'Name and audio file are required' },
        { status: 400 }
      )
    }

    // Validate audio file
    const maxSize = 10 * 1024 * 1024 // 10MB
    if (audioFile.size > maxSize) {
      return NextResponse.json(
        { error: 'Audio file must be less than 10MB' },
        { status: 400 }
      )
    }

    const allowedTypes = ['audio/wav', 'audio/mp3', 'audio/mpeg', 'audio/webm', 'audio/ogg']
    if (!allowedTypes.includes(audioFile.type)) {
      return NextResponse.json(
        { error: 'Invalid audio format. Please use WAV, MP3, WebM, or OGG' },
        { status: 400 }
      )
    }

    // Clone voice using ElevenLabs
    console.log('Cloning voice with ElevenLabs...')
    const clonedVoice = await elevenLabsService.cloneVoice(
      name,
      description || `Voice clone: ${name}`,
      [audioFile]
    )

    // Store voice model in database
    let voiceModel = null
    try {
      voiceModel = await voiceModelService.create({
        name,
        description,
        voice_id: clonedVoice.voice_id,
        audio_url: '', // We'll store the original audio URL if needed
      })
    } catch (dbError) {
      console.warn('Failed to store voice model in database:', dbError)
      // Create a temporary voice model object for the response
      voiceModel = {
        id: clonedVoice.voice_id, // Use ElevenLabs voice_id as fallback
        name,
        description,
        voice_id: clonedVoice.voice_id,
        audio_url: '',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
    }

    return NextResponse.json({
      success: true,
      voiceModel,
      elevenLabsVoice: clonedVoice
    })

  } catch (error) {
    console.error('Voice cloning error:', error)
    
    // Handle specific ElevenLabs errors
    if (error instanceof Error) {
      if (error.message.includes('quota')) {
        return NextResponse.json(
          { error: 'API quota exceeded. Please try again later.' },
          { status: 429 }
        )
      }
      if (error.message.includes('unauthorized')) {
        return NextResponse.json(
          { error: 'Invalid API key configuration' },
          { status: 401 }
        )
      }
    }

    return NextResponse.json(
      { error: 'Failed to clone voice. Please try again.' },
      { status: 500 }
    )
  }
}

export async function GET() {
  try {
    // Get all voice models from database
    const voiceModels = await voiceModelService.getAll()

    return NextResponse.json({
      success: true,
      voiceModels
    })
  } catch (error) {
    console.error('Error fetching voice models:', error)

    // Return empty array if database is not available
    // This allows the app to work without Supabase for demo purposes
    return NextResponse.json({
      success: true,
      voiceModels: []
    })
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const voiceId = searchParams.get('voiceId')

    if (!voiceId) {
      return NextResponse.json(
        { error: 'Voice ID is required' },
        { status: 400 }
      )
    }

    // Get voice model from database
    const voiceModel = await voiceModelService.getById(voiceId)
    if (!voiceModel) {
      return NextResponse.json(
        { error: 'Voice model not found' },
        { status: 404 }
      )
    }

    // Delete from ElevenLabs
    await elevenLabsService.deleteVoice(voiceModel.voice_id)

    // Delete from database
    await voiceModelService.delete(voiceId)

    return NextResponse.json({
      success: true,
      message: 'Voice model deleted successfully'
    })

  } catch (error) {
    console.error('Voice deletion error:', error)
    return NextResponse.json(
      { error: 'Failed to delete voice model' },
      { status: 500 }
    )
  }
}
