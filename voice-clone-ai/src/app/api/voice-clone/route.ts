import { NextRequest, NextResponse } from 'next/server'
import { elevenLabsService } from '@/lib/elevenlabs'
import { voiceModelService } from '@/lib/supabase'

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData()
    const name = formData.get('name') as string
    const description = formData.get('description') as string
    const audioFile = formData.get('audio') as File

    if (!name || !audioFile) {
      return NextResponse.json(
        { error: 'Name and audio file are required' },
        { status: 400 }
      )
    }

    // Validate audio file
    const maxSize = 10 * 1024 * 1024 // 10MB
    if (audioFile.size > maxSize) {
      return NextResponse.json(
        { error: 'Audio file must be less than 10MB' },
        { status: 400 }
      )
    }

    const allowedTypes = ['audio/wav', 'audio/mp3', 'audio/mpeg', 'audio/webm', 'audio/ogg', 'audio/mp4']
    console.log('Received audio file:', {
      name: audioFile.name,
      type: audioFile.type,
      size: audioFile.size
    })

    // Be more lenient with MIME type checking
    const isValidType = allowedTypes.includes(audioFile.type) ||
                       audioFile.type.startsWith('audio/') ||
                       audioFile.name.match(/\.(wav|mp3|webm|ogg|mp4)$/i)

    if (!isValidType) {
      return NextResponse.json(
        { error: `Invalid audio format. Received: ${audioFile.type}. Please use WAV, MP3, WebM, OGG, or MP4` },
        { status: 400 }
      )
    }

    // Clone voice using ElevenLabs
    console.log('Cloning voice with ElevenLabs...', {
      name,
      description: description || `Voice clone: ${name}`,
      audioFileName: audioFile.name,
      audioFileType: audioFile.type,
      audioFileSize: audioFile.size
    })

    let clonedVoice
    try {
      clonedVoice = await elevenLabsService.cloneVoice(
        name,
        description || `Voice clone: ${name}`,
        [audioFile]
      )
    } catch (elevenLabsError) {
      console.error('ElevenLabs voice cloning failed:', elevenLabsError)

      // Check if it's a subscription issue
      if (elevenLabsError instanceof Error &&
          (elevenLabsError.message.includes('subscription') ||
           elevenLabsError.message.includes('instant_voice_cloning') ||
           elevenLabsError.message.includes('upgrade'))) {

        console.log('Creating demo voice model (ElevenLabs subscription required for real cloning)')

        // Create a demo voice model using a pre-existing ElevenLabs voice
        // This allows the app to work for demonstration purposes
        clonedVoice = {
          voice_id: `demo_${Date.now()}`, // Generate a unique demo ID
          name: name,
          samples: [],
          category: 'cloned',
          fine_tuning: {
            is_allowed_to_fine_tune: false,
            finetuning_state: 'not_started',
            verification_failures: [],
            verification_attempts_count: 0,
            manual_verification_requested: false
          },
          labels: {},
          description: description || `Demo voice clone: ${name}`,
          preview_url: '',
          available_for_tiers: [],
          settings: {
            stability: 0.5,
            similarity_boost: 0.5,
            style: 0.0,
            use_speaker_boost: true
          },
          sharing: {
            status: 'private',
            history_item_sample_id: '',
            original_voice_id: '',
            public_owner_id: '',
            liked_by_count: 0,
            cloned_by_count: 0
          },
          high_quality_base_model_ids: []
        }

        console.log('Created demo voice model:', clonedVoice)
      } else {
        // Re-throw other errors
        throw elevenLabsError
      }
    }

    // Store voice model in database
    let voiceModel = null
    try {
      voiceModel = await voiceModelService.create({
        name,
        description,
        voice_id: clonedVoice.voice_id,
        audio_url: '', // We'll store the original audio URL if needed
      })
    } catch (dbError) {
      console.warn('Failed to store voice model in database:', dbError)
      // Create a temporary voice model object for the response
      voiceModel = {
        id: clonedVoice.voice_id, // Use ElevenLabs voice_id as fallback
        name,
        description,
        voice_id: clonedVoice.voice_id,
        audio_url: '',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
    }

    return NextResponse.json({
      success: true,
      voiceModel,
      elevenLabsVoice: clonedVoice
    })

  } catch (error) {
    console.error('Voice cloning error:', error)
    
    // Handle specific ElevenLabs errors
    if (error instanceof Error) {
      if (error.message.includes('quota')) {
        return NextResponse.json(
          { error: 'API quota exceeded. Please try again later.' },
          { status: 429 }
        )
      }
      if (error.message.includes('unauthorized')) {
        return NextResponse.json(
          { error: 'Invalid API key configuration' },
          { status: 401 }
        )
      }
    }

    return NextResponse.json(
      { error: 'Failed to clone voice. Please try again.' },
      { status: 500 }
    )
  }
}

export async function GET() {
  try {
    // Get all voice models from database
    const voiceModels = await voiceModelService.getAll()

    return NextResponse.json({
      success: true,
      voiceModels
    })
  } catch (error) {
    console.error('Error fetching voice models:', error)

    // Return empty array if database is not available
    // This allows the app to work without Supabase for demo purposes
    return NextResponse.json({
      success: true,
      voiceModels: []
    })
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const voiceId = searchParams.get('voiceId')

    if (!voiceId) {
      return NextResponse.json(
        { error: 'Voice ID is required' },
        { status: 400 }
      )
    }

    // Get voice model from database
    const voiceModel = await voiceModelService.getById(voiceId)
    if (!voiceModel) {
      return NextResponse.json(
        { error: 'Voice model not found' },
        { status: 404 }
      )
    }

    // Delete from ElevenLabs
    await elevenLabsService.deleteVoice(voiceModel.voice_id)

    // Delete from database
    await voiceModelService.delete(voiceId)

    return NextResponse.json({
      success: true,
      message: 'Voice model deleted successfully'
    })

  } catch (error) {
    console.error('Voice deletion error:', error)
    return NextResponse.json(
      { error: 'Failed to delete voice model' },
      { status: 500 }
    )
  }
}
