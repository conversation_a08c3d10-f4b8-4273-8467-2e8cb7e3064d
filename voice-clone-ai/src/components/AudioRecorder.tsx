'use client'

import { useState, useRef, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Mic, Square, Play, Pause, Download, Upload, Trash2, Volume2 } from 'lucide-react'
import { AudioRecorder as AudioRecorderClass, audioUtils } from '@/lib/audio'

interface AudioRecorderProps {
  onRecordingComplete?: (audioBlob: Blob) => void
  onRecordingStart?: () => void
  onRecordingStop?: () => void
  maxDuration?: number // in seconds
  className?: string
}

export default function AudioRecorder({
  onRecordingComplete,
  onRecordingStart,
  onRecordingStop,
  maxDuration = 120, // 2 minutes default
  className = ''
}: AudioRecorderProps) {
  const [isRecording, setIsRecording] = useState(false)
  const [isPlaying, setIsPlaying] = useState(false)
  const [audioBlob, setAudioBlob] = useState<Blob | null>(null)
  const [audioUrl, setAudioUrl] = useState<string | null>(null)
  const [duration, setDuration] = useState(0)
  const [audioLevel, setAudioLevel] = useState(0)
  const [error, setError] = useState<string | null>(null)
  const [isInitialized, setIsInitialized] = useState(false)
  const [permissionStatus, setPermissionStatus] = useState<'unknown' | 'granted' | 'denied'>('unknown')

  const recorderRef = useRef<AudioRecorderClass | null>(null)
  const audioRef = useRef<HTMLAudioElement | null>(null)
  const intervalRef = useRef<NodeJS.Timeout | null>(null)
  const animationRef = useRef<number | null>(null)

  useEffect(() => {
    // Check initial permission status and auto-initialize if granted
    const initializeIfPermitted = async () => {
      await checkMicrophonePermission()

      // If permission is already granted, auto-initialize
      if (permissionStatus === 'granted') {
        await initializeRecorder()
      }
    }

    initializeIfPermitted()

    return () => {
      cleanup()
    }
  }, [])

  // Auto-initialize when permission status changes to granted
  useEffect(() => {
    if (permissionStatus === 'granted' && !isInitialized) {
      initializeRecorder()
    }
  }, [permissionStatus, isInitialized])

  const checkMicrophonePermission = async () => {
    try {
      if (navigator.permissions) {
        const permission = await navigator.permissions.query({ name: 'microphone' as PermissionName })
        setPermissionStatus(permission.state as 'granted' | 'denied')

        permission.onchange = () => {
          setPermissionStatus(permission.state as 'granted' | 'denied')
        }
      } else {
        // Fallback: try to access microphone to check permission
        try {
          const stream = await navigator.mediaDevices.getUserMedia({ audio: true })
          stream.getTracks().forEach(track => track.stop())
          setPermissionStatus('granted')
        } catch (err) {
          setPermissionStatus('denied')
        }
      }
    } catch (err) {
      console.log('Permission check failed:', err)
      setPermissionStatus('unknown')
    }
  }

  const requestMicrophonePermission = async () => {
    try {
      setError(null)
      console.log('Requesting microphone permission...')

      const stream = await navigator.mediaDevices.getUserMedia({
        audio: {
          sampleRate: 44100,
          channelCount: 1,
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true,
        }
      })

      // Stop the test stream immediately
      stream.getTracks().forEach(track => track.stop())

      setPermissionStatus('granted')
      setError(null)

      // Auto-initialize after permission granted
      await initializeRecorder()

    } catch (err) {
      console.error('Permission request failed:', err)
      setPermissionStatus('denied')

      if (err instanceof Error) {
        if (err.name === 'NotAllowedError') {
          setError('Microphone access denied. Please click the microphone icon in your browser\'s address bar and allow access.')
        } else if (err.name === 'NotFoundError') {
          setError('No microphone found. Please connect a microphone and try again.')
        } else {
          setError(`Microphone error: ${err.message}`)
        }
      }
    }
  }

  const cleanup = () => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current)
    }
    if (animationRef.current) {
      cancelAnimationFrame(animationRef.current)
    }
    if (recorderRef.current) {
      recorderRef.current.cleanup()
    }
    if (audioUrl) {
      URL.revokeObjectURL(audioUrl)
    }
  }

  const initializeRecorder = async () => {
    try {
      setError(null)

      // Check if browser supports required APIs
      if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
        throw new Error('Your browser does not support audio recording')
      }

      // Request microphone permission explicitly
      console.log('Requesting microphone permission...')
      const stream = await navigator.mediaDevices.getUserMedia({
        audio: {
          sampleRate: 44100,
          channelCount: 1,
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true,
        }
      })

      // Stop the test stream
      stream.getTracks().forEach(track => track.stop())

      // Now initialize the recorder
      recorderRef.current = new AudioRecorderClass()
      await recorderRef.current.initialize()
      setIsInitialized(true)
      console.log('Audio recorder initialized successfully')
    } catch (err) {
      console.error('Recorder initialization failed:', err)
      if (err instanceof Error) {
        if (err.name === 'NotAllowedError') {
          setError('Microphone access denied. Please allow microphone permissions and refresh the page.')
        } else if (err.name === 'NotFoundError') {
          setError('No microphone found. Please connect a microphone and try again.')
        } else if (err.name === 'NotSupportedError') {
          setError('Your browser does not support audio recording.')
        } else {
          setError(`Microphone error: ${err.message}`)
        }
      } else {
        setError('Failed to access microphone. Please check permissions.')
      }
    }
  }

  const startRecording = async () => {
    try {
      setError(null)

      if (!recorderRef.current || !isInitialized) {
        console.log('Initializing recorder...')
        await initializeRecorder()
      }

      if (!recorderRef.current || !isInitialized) {
        setError('Failed to initialize microphone. Please check permissions and try again.')
        return
      }

      console.log('Starting recording...')
      setDuration(0)
      recorderRef.current.startRecording()
      setIsRecording(true)
      onRecordingStart?.()

      // Start duration timer
      intervalRef.current = setInterval(() => {
        setDuration(prev => {
          const newDuration = prev + 1
          if (newDuration >= maxDuration) {
            stopRecording()
          }
          return newDuration
        })
      }, 1000)

      // Start audio level monitoring
      const updateAudioLevel = () => {
        if (recorderRef.current && isRecording) {
          setAudioLevel(recorderRef.current.getAudioLevel())
          animationRef.current = requestAnimationFrame(updateAudioLevel)
        }
      }
      updateAudioLevel()
    } catch (err) {
      console.error('Recording start failed:', err)
      setError('Failed to start recording. Please check microphone permissions.')
    }
  }

  const stopRecording = async () => {
    if (!recorderRef.current || !isRecording) return

    try {
      const blob = await recorderRef.current.stopRecording()
      setIsRecording(false)
      setAudioLevel(0)
      onRecordingStop?.()

      if (intervalRef.current) {
        clearInterval(intervalRef.current)
      }
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current)
      }

      // Clean up previous audio URL
      if (audioUrl) {
        URL.revokeObjectURL(audioUrl)
      }

      const newAudioUrl = audioUtils.createAudioURL(blob)
      setAudioBlob(blob)
      setAudioUrl(newAudioUrl)
      onRecordingComplete?.(blob)
    } catch (err) {
      setError('Failed to stop recording')
      console.error('Recording stop failed:', err)
    }
  }

  const playAudio = () => {
    if (!audioRef.current || !audioUrl) return

    if (isPlaying) {
      audioRef.current.pause()
      setIsPlaying(false)
    } else {
      audioRef.current.play()
      setIsPlaying(true)
    }
  }

  const downloadAudio = () => {
    if (!audioBlob) return
    audioUtils.downloadAudio(audioBlob, `voice-recording-${Date.now()}.webm`)
  }

  const deleteRecording = () => {
    if (audioUrl) {
      URL.revokeObjectURL(audioUrl)
    }
    setAudioBlob(null)
    setAudioUrl(null)
    setDuration(0)
    setIsPlaying(false)
  }

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    const validation = audioUtils.validateAudioFile(file)
    if (!validation.valid) {
      setError(validation.error || 'Invalid audio file')
      return
    }

    setError(null)
    const blob = new Blob([file], { type: file.type })
    const url = audioUtils.createAudioURL(blob)
    
    if (audioUrl) {
      URL.revokeObjectURL(audioUrl)
    }
    
    setAudioBlob(blob)
    setAudioUrl(url)
    onRecordingComplete?.(blob)
  }

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Error Display */}
      <AnimatePresence>
        {error && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            className="bg-red-900/50 border border-red-700 rounded-lg p-4 text-red-200"
          >
            {error}
          </motion.div>
        )}
      </AnimatePresence>

      {/* Recording Controls */}
      <div className="card">
        <div className="text-center mb-6">
          <h3 className="text-xl font-semibold mb-2">Record Your Voice</h3>
          <p className="text-gray-400 mb-2">
            Record at least 30 seconds for best results (max {maxDuration}s)
          </p>
          {permissionStatus === 'unknown' && (
            <p className="text-sm text-blue-300">
              💡 On macOS: Make sure to allow microphone access in System Preferences → Security & Privacy → Microphone
            </p>
          )}
        </div>

        {/* Audio Visualizer */}
        <div className="flex justify-center mb-6">
          <div className="audio-visualizer">
            {[...Array(12)].map((_, i) => (
              <div
                key={i}
                className="audio-bar w-3"
                style={{
                  height: isRecording 
                    ? `${Math.max(4, audioLevel * 60 + Math.random() * 20)}px`
                    : '4px'
                }}
              />
            ))}
          </div>
        </div>

        {/* Timer */}
        <div className="text-center mb-6">
          <div className="text-2xl font-mono text-purple-400">
            {formatTime(duration)}
          </div>
          {maxDuration && (
            <div className="text-sm text-gray-400">
              / {formatTime(maxDuration)}
            </div>
          )}
        </div>

        {/* Permission Status */}
        {permissionStatus === 'denied' && (
          <div className="mb-6 p-4 bg-yellow-900/50 border border-yellow-700 rounded-lg">
            <div className="flex items-center space-x-2 text-yellow-200 mb-2">
              <Mic className="w-5 h-5" />
              <span className="font-medium">Microphone Permission Required</span>
            </div>
            <p className="text-sm text-yellow-300 mb-3">
              Please allow microphone access to record your voice. Click the button below to request permission.
            </p>
            <button
              onClick={requestMicrophonePermission}
              className="btn-primary flex items-center space-x-2"
            >
              <Mic className="w-4 h-4" />
              <span>Allow Microphone Access</span>
            </button>
          </div>
        )}

        {/* Main Controls */}
        <div className="flex justify-center space-x-4 mb-6">
          {(permissionStatus === 'unknown' || !isInitialized) && (
            <button
              onClick={requestMicrophonePermission}
              className="btn-primary flex items-center space-x-2"
            >
              <Mic className="w-5 h-5" />
              <span>Enable Microphone</span>
            </button>
          )}

          {permissionStatus === 'granted' && isInitialized && !isRecording && (
            <button
              onClick={startRecording}
              className="btn-primary flex items-center space-x-2"
              disabled={isRecording}
            >
              <Mic className="w-5 h-5" />
              <span>Start Recording</span>
            </button>
          )}

          {isRecording && (
            <button
              onClick={stopRecording}
              className="bg-red-600 hover:bg-red-700 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-200 flex items-center space-x-2"
            >
              <Square className="w-5 h-5" />
              <span>Stop Recording</span>
            </button>
          )}

          {/* File Upload */}
          <label className="btn-secondary cursor-pointer flex items-center space-x-2">
            <Upload className="w-5 h-5" />
            <span>Upload Audio</span>
            <input
              type="file"
              accept="audio/*"
              onChange={handleFileUpload}
              className="hidden"
            />
          </label>
        </div>

        {/* Recording Progress */}
        {isRecording && (
          <div className="w-full bg-gray-700 rounded-full h-2 mb-4">
            <div
              className="bg-gradient-to-r from-purple-600 to-blue-600 h-2 rounded-full transition-all duration-1000"
              style={{ width: `${(duration / maxDuration) * 100}%` }}
            />
          </div>
        )}
      </div>

      {/* Playback Controls */}
      {audioUrl && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="card"
        >
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <button
                onClick={playAudio}
                className="btn-secondary flex items-center space-x-2"
              >
                {isPlaying ? (
                  <Pause className="w-4 h-4" />
                ) : (
                  <Play className="w-4 h-4" />
                )}
                <span>{isPlaying ? 'Pause' : 'Play'}</span>
              </button>

              <div className="flex items-center space-x-2 text-gray-400">
                <Volume2 className="w-4 h-4" />
                <span>Recording ready</span>
              </div>
            </div>

            <div className="flex items-center space-x-2">
              <button
                onClick={downloadAudio}
                className="btn-secondary flex items-center space-x-2"
              >
                <Download className="w-4 h-4" />
                <span>Download</span>
              </button>

              <button
                onClick={deleteRecording}
                className="text-red-400 hover:text-red-300 p-2 rounded-lg hover:bg-red-900/20 transition-colors"
              >
                <Trash2 className="w-4 h-4" />
              </button>
            </div>
          </div>

          <audio
            ref={audioRef}
            src={audioUrl}
            onEnded={() => setIsPlaying(false)}
            onPlay={() => setIsPlaying(true)}
            onPause={() => setIsPlaying(false)}
            className="hidden"
          />
        </motion.div>
      )}
    </div>
  )
}
