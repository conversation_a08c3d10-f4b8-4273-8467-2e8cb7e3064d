# 🎤 VoiceClone AI

An advanced AI-powered voice cloning application that allows you to clone your voice and generate speech from any text. Built with Next.js, TypeScript, and ElevenLabs AI.

![VoiceClone AI](https://img.shields.io/badge/VoiceClone-AI-purple?style=for-the-badge)
![Next.js](https://img.shields.io/badge/Next.js-15.3.3-black?style=for-the-badge&logo=next.js)
![TypeScript](https://img.shields.io/badge/TypeScript-5.0-blue?style=for-the-badge&logo=typescript)
![Tailwind CSS](https://img.shields.io/badge/Tailwind-CSS-38B2AC?style=for-the-badge&logo=tailwind-css)

## ✨ Features

- 🎙️ **Voice Recording**: High-quality audio recording with real-time visualization
- 🧠 **AI Voice Cloning**: Advanced voice replication using ElevenLabs API
- 🔊 **Speech Generation**: Convert any text to speech in your cloned voice
- ⚙️ **Voice Settings**: Fine-tune stability, similarity, and style parameters
- 📱 **Responsive Design**: Works perfectly on desktop and mobile devices
- 🎨 **Beautiful UI**: Modern dark theme with smooth animations
- 💾 **Data Management**: Store and manage voice models and generated audio

## 🚀 Quick Start

### Prerequisites

- Node.js 18+ installed
- ElevenLabs account (free tier available)
- Supabase account (free tier available)

### 1. Environment Setup

Your `.env.local` file should contain:

```env
# ElevenLabs API Configuration
NEXT_PUBLIC_ELEVENLABS_API_KEY=your_elevenlabs_api_key_here

# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url_here
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key_here

# App Configuration
NEXT_PUBLIC_APP_URL=http://localhost:3000
```

### 2. Database Setup

1. Open your Supabase project dashboard
2. Go to SQL Editor
3. Copy and paste the contents of `supabase-setup.sql`
4. Run the SQL script

### 3. Run the Application

```bash
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) in your browser.

## 🛠️ Tech Stack

- **Frontend**: Next.js 14, TypeScript, Tailwind CSS
- **Animations**: Framer Motion
- **Icons**: Lucide React
- **Voice AI**: ElevenLabs API
- **Database**: Supabase (PostgreSQL)
- **Audio**: Web Audio API, MediaRecorder API
- **Deployment**: Vercel-ready

## 📖 Usage

### 1. Create a Voice Clone

1. Click "Start Cloning Now" on the homepage
2. Enter a name and description for your voice
3. Record at least 30 seconds of clear speech
4. Wait for AI processing to complete

### 2. Generate Speech

1. Navigate to the Generate page
2. Select your voice model
3. Enter the text you want to convert
4. Adjust voice settings if needed
5. Click "Generate Speech"
6. Play or download the generated audio

## 🔧 Configuration

### Audio Constraints

- **Recording**: Minimum 30 seconds, maximum 2 minutes
- **File Upload**: Maximum 10MB, supports WAV, MP3, WebM, OGG
- **Text Input**: Maximum 5,000 characters per generation

## 🚀 Deployment

Deploy to Vercel by connecting your GitHub repository and adding the environment variables.

**Built with ❤️ and AI magic** 🎤✨
