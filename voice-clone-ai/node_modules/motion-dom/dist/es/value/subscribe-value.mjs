import { cancelFrame, frame } from '../frameloop/frame.mjs';

function subscribeValue(inputValues, outputValue, getLatest) {
    const update = () => outputValue.set(getLatest());
    const scheduleUpdate = () => frame.preRender(update, false, true);
    const subscriptions = inputValues.map((v) => v.on("change", scheduleUpdate));
    outputValue.on("destroy", () => {
        subscriptions.forEach((unsubscribe) => unsubscribe());
        cancelFrame(update);
    });
}

export { subscribeValue };
