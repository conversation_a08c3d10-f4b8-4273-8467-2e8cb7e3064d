-- Voice Clone AI Database Setup
-- Run this SQL in your Supabase SQL Editor

-- Create voice_models table
CREATE TABLE IF NOT EXISTS voice_models (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name TEXT NOT NULL,
  description TEXT,
  voice_id TEXT NOT NULL UNIQUE,
  audio_url TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create generated_audio table
CREATE TABLE IF NOT EXISTS generated_audio (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  voice_model_id UUID REFERENCES voice_models(id) ON DELETE CASCADE,
  text TEXT NOT NULL,
  audio_url TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_voice_models_created_at ON voice_models(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_voice_models_voice_id ON voice_models(voice_id);
CREATE INDEX IF NOT EXISTS idx_generated_audio_voice_model_id ON generated_audio(voice_model_id);
CREATE INDEX IF NOT EXISTS idx_generated_audio_created_at ON generated_audio(created_at DESC);

-- Enable Row Level Security (RLS)
ALTER TABLE voice_models ENABLE ROW LEVEL SECURITY;
ALTER TABLE generated_audio ENABLE ROW LEVEL SECURITY;

-- Create policies for public access (you can modify these for user-specific access later)
CREATE POLICY "Allow all operations on voice_models" ON voice_models
  FOR ALL USING (true) WITH CHECK (true);

CREATE POLICY "Allow all operations on generated_audio" ON generated_audio
  FOR ALL USING (true) WITH CHECK (true);

-- Create a function to automatically update the updated_at column
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger to automatically update updated_at
CREATE TRIGGER update_voice_models_updated_at 
  BEFORE UPDATE ON voice_models 
  FOR EACH ROW 
  EXECUTE FUNCTION update_updated_at_column();

-- Insert some sample data (optional)
-- You can uncomment these lines if you want some test data

-- INSERT INTO voice_models (name, description, voice_id, audio_url) VALUES
-- ('Sample Voice 1', 'A demo voice for testing', 'demo-voice-1', 'https://example.com/sample1.mp3'),
-- ('Sample Voice 2', 'Another demo voice', 'demo-voice-2', 'https://example.com/sample2.mp3');

-- Verify the setup
SELECT 'voice_models table created successfully' as status;
SELECT 'generated_audio table created successfully' as status;
SELECT 'Database setup complete!' as status;
