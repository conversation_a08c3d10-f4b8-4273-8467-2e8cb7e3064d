"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/generate/page",{

/***/ "(app-pages-browser)/./src/app/generate/page.tsx":
/*!***********************************!*\
  !*** ./src/app/generate/page.tsx ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ GeneratePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Download_Mic_Pause_Play_Settings_Sparkles_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Download,Mic,Pause,Play,Settings,Sparkles,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Download_Mic_Pause_Play_Settings_Sparkles_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Download,Mic,Pause,Play,Settings,Sparkles,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Download_Mic_Pause_Play_Settings_Sparkles_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Download,Mic,Pause,Play,Settings,Sparkles,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mic.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Download_Mic_Pause_Play_Settings_Sparkles_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Download,Mic,Pause,Play,Settings,Sparkles,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Download_Mic_Pause_Play_Settings_Sparkles_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Download,Mic,Pause,Play,Settings,Sparkles,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/volume-2.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Download_Mic_Pause_Play_Settings_Sparkles_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Download,Mic,Pause,Play,Settings,Sparkles,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pause.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Download_Mic_Pause_Play_Settings_Sparkles_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Download,Mic,Pause,Play,Settings,Sparkles,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Download_Mic_Pause_Play_Settings_Sparkles_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Download,Mic,Pause,Play,Settings,Sparkles,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _lib_localStorage__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/localStorage */ \"(app-pages-browser)/./src/lib/localStorage.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction GeneratePage() {\n    _s();\n    const [voiceModels, setVoiceModels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedVoice, setSelectedVoice] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [text, setText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isGenerating, setIsGenerating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isPlaying, setIsPlaying] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [audioUrl, setAudioUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showSettings, setShowSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [settings, setSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        stability: 0.5,\n        similarity_boost: 0.5,\n        style: 0.0,\n        use_speaker_boost: true\n    });\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [coquiServerReady, setCoquiServerReady] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const audioRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"GeneratePage.useEffect\": ()=>{\n            fetchVoiceModels();\n        }\n    }[\"GeneratePage.useEffect\"], []);\n    const fetchVoiceModels = async ()=>{\n        try {\n            // Get Coqui voice models\n            const response = await fetch('/api/coqui-voice-clone');\n            const data = await response.json();\n            let allVoiceModels = [];\n            if (data.success) {\n                allVoiceModels = data.voiceModels || [];\n                setCoquiServerReady(data.serverRunning || false);\n            }\n            // Also check localStorage for voice models (fallback)\n            const localModels = _lib_localStorage__WEBPACK_IMPORTED_MODULE_3__.localStorageService.getVoiceModels();\n            if (localModels.length > 0) {\n                // Convert local models to the expected format\n                const convertedLocalModels = localModels.map((model)=>({\n                        ...model,\n                        updated_at: model.created_at\n                    }));\n                // Merge with API models, avoiding duplicates\n                const existingVoiceIds = allVoiceModels.map((m)=>m.voice_id);\n                const newLocalModels = convertedLocalModels.filter((model)=>!existingVoiceIds.includes(model.voice_id));\n                allVoiceModels = [\n                    ...allVoiceModels,\n                    ...newLocalModels\n                ];\n            }\n            setVoiceModels(allVoiceModels);\n            if (allVoiceModels.length > 0) {\n                setSelectedVoice(allVoiceModels[0]);\n            }\n        } catch (error) {\n            console.error('Failed to fetch voice models:', error);\n            // Fallback to localStorage only\n            const localModels = _lib_localStorage__WEBPACK_IMPORTED_MODULE_3__.localStorageService.getVoiceModels();\n            if (localModels.length > 0) {\n                const convertedLocalModels = localModels.map((model)=>({\n                        ...model,\n                        updated_at: model.created_at\n                    }));\n                setVoiceModels(convertedLocalModels);\n                setSelectedVoice(convertedLocalModels[0]);\n            } else {\n                setError('Failed to load voice models');\n            }\n        }\n    };\n    const generateSpeech = async ()=>{\n        if (!selectedVoice || !text.trim()) return;\n        setIsGenerating(true);\n        setError(null);\n        try {\n            const response = await fetch('/api/coqui-generate-speech', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    text: text.trim(),\n                    voiceModelId: selectedVoice.id,\n                    settings: {\n                        speed: settings.stability,\n                        language: 'en',\n                        model: 'tts_models/multilingual/multi-dataset/xtts_v2'\n                    }\n                })\n            });\n            if (!response.ok) {\n                const errorData = await response.json();\n                throw new Error(errorData.error || errorData.message || 'Failed to generate speech');\n            }\n            // Get audio blob from response\n            const audioBlob = await response.blob();\n            // Clean up previous audio URL\n            if (audioUrl) {\n                URL.revokeObjectURL(audioUrl);\n            }\n            // Create new audio URL\n            const newAudioUrl = URL.createObjectURL(audioBlob);\n            setAudioUrl(newAudioUrl);\n        } catch (error) {\n            console.error('Speech generation failed:', error);\n            setError(error instanceof Error ? error.message : 'Failed to generate speech');\n        } finally{\n            setIsGenerating(false);\n        }\n    };\n    const playAudio = ()=>{\n        if (!audioRef.current || !audioUrl) return;\n        if (isPlaying) {\n            audioRef.current.pause();\n            setIsPlaying(false);\n        } else {\n            audioRef.current.play();\n            setIsPlaying(true);\n        }\n    };\n    const downloadAudio = ()=>{\n        if (!audioUrl) return;\n        const a = document.createElement('a');\n        a.href = audioUrl;\n        a.download = \"voice-clone-\".concat(Date.now(), \".mp3\");\n        document.body.appendChild(a);\n        a.click();\n        document.body.removeChild(a);\n    };\n    const sampleTexts = [\n        \"Hello, this is my AI voice clone speaking. How do I sound?\",\n        \"Welcome to the future of voice technology. Your voice, unlimited possibilities.\",\n        \"I can read any text you give me in your own voice. Pretty amazing, right?\",\n        \"The quick brown fox jumps over the lazy dog. This sentence contains every letter of the alphabet.\"\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen py-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"fixed top-0 w-full z-50 bg-black/80 backdrop-blur-md border-b border-gray-800\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center h-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/\",\n                                className: \"flex items-center space-x-2 text-gray-300 hover:text-white transition-colors\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Download_Mic_Pause_Play_Settings_Sparkles_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                        lineNumber: 186,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Back to Home\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                        lineNumber: 187,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                lineNumber: 185,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Download_Mic_Pause_Play_Settings_Sparkles_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"w-8 h-8 text-purple-400\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                        lineNumber: 190,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xl font-bold gradient-text\",\n                                        children: \"VoiceClone AI\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                        lineNumber: 191,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                lineNumber: 189,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                        lineNumber: 184,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                    lineNumber: 183,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                lineNumber: 182,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"pt-24 px-4 sm:px-6 lg:px-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-6xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-4xl md:text-5xl font-bold mb-4\",\n                                    children: [\n                                        \"Generate \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"gradient-text\",\n                                            children: \"Speech\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                            lineNumber: 201,\n                                            columnNumber: 24\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                    lineNumber: 200,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl text-gray-300 mb-4\",\n                                    children: \"Type any text and hear it spoken in your cloned voice using Coqui TTS\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                    lineNumber: 203,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"inline-flex items-center px-4 py-2 bg-blue-900/30 border border-blue-700 rounded-lg text-blue-300\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm\",\n                                        children: \"\\uD83D\\uDE80 Powered by Coqui TTS - Free & Open Source\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                        lineNumber: 207,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                    lineNumber: 206,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                            lineNumber: 199,\n                            columnNumber: 11\n                        }, this),\n                        error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: -10\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            className: \"bg-red-900/50 border border-red-700 rounded-lg p-4 text-red-200 mb-6 max-w-2xl mx-auto\",\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                            lineNumber: 212,\n                            columnNumber: 13\n                        }, this),\n                        !coquiServerReady && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"card\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xl font-semibold mb-4\",\n                                        children: \"Coqui TTS Server Required\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                        lineNumber: 225,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-300 mb-4\",\n                                        children: [\n                                            \"To use voice cloning, you need to start the Coqui TTS server. The server is currently \",\n                                            coquiServerReady ? 'running' : 'not running',\n                                            \".\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                        lineNumber: 226,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>{\n                                            setCoquiServerReady(true);\n                                            fetchVoiceModels();\n                                        },\n                                        className: \"btn-primary\",\n                                        children: \"Mark Server as Ready\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                        lineNumber: 230,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                lineNumber: 224,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                            lineNumber: 223,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid lg:grid-cols-3 gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"lg:col-span-1\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"card\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-xl font-semibold mb-4 flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Download_Mic_Pause_Play_Settings_Sparkles_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        className: \"w-5 h-5 mr-2 text-purple-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                        lineNumber: 248,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Select Voice\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                lineNumber: 247,\n                                                columnNumber: 17\n                                            }, this),\n                                            voiceModels.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center py-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-400 mb-4\",\n                                                        children: \"No voice models found\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                        lineNumber: 254,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: \"/record\",\n                                                        className: \"btn-primary\",\n                                                        children: \"Create Voice Clone\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                        lineNumber: 255,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                lineNumber: 253,\n                                                columnNumber: 19\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: voiceModels.map((voice)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setSelectedVoice(voice),\n                                                        className: \"w-full p-4 rounded-lg border transition-all duration-200 text-left \".concat((selectedVoice === null || selectedVoice === void 0 ? void 0 : selectedVoice.id) === voice.id ? 'border-purple-500 bg-purple-900/20' : 'border-gray-700 hover:border-gray-600'),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"font-medium\",\n                                                                children: voice.name\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                                lineNumber: 271,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            voice.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-gray-400 mt-1\",\n                                                                children: voice.description\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                                lineNumber: 273,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-gray-500 mt-2\",\n                                                                children: [\n                                                                    \"Created \",\n                                                                    new Date(voice.created_at).toLocaleDateString()\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                                lineNumber: 277,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, voice.id, true, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                        lineNumber: 262,\n                                                        columnNumber: 23\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                lineNumber: 260,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setShowSettings(!showSettings),\n                                                        className: \"btn-secondary w-full flex items-center justify-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Download_Mic_Pause_Play_Settings_Sparkles_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                className: \"w-4 h-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                                lineNumber: 291,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"Voice Settings\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                                lineNumber: 292,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                        lineNumber: 287,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    showSettings && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                                        initial: {\n                                                            opacity: 0,\n                                                            height: 0\n                                                        },\n                                                        animate: {\n                                                            opacity: 1,\n                                                            height: 'auto'\n                                                        },\n                                                        className: \"mt-4 space-y-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium mb-2\",\n                                                                        children: [\n                                                                            \"Stability: \",\n                                                                            settings.stability.toFixed(2)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                                        lineNumber: 302,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"range\",\n                                                                        min: \"0\",\n                                                                        max: \"1\",\n                                                                        step: \"0.01\",\n                                                                        value: settings.stability,\n                                                                        onChange: (e)=>setSettings((prev)=>({\n                                                                                    ...prev,\n                                                                                    stability: parseFloat(e.target.value)\n                                                                                })),\n                                                                        className: \"w-full\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                                        lineNumber: 305,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                                lineNumber: 301,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium mb-2\",\n                                                                        children: [\n                                                                            \"Similarity: \",\n                                                                            settings.similarity_boost.toFixed(2)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                                        lineNumber: 317,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"range\",\n                                                                        min: \"0\",\n                                                                        max: \"1\",\n                                                                        step: \"0.01\",\n                                                                        value: settings.similarity_boost,\n                                                                        onChange: (e)=>setSettings((prev)=>({\n                                                                                    ...prev,\n                                                                                    similarity_boost: parseFloat(e.target.value)\n                                                                                })),\n                                                                        className: \"w-full\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                                        lineNumber: 320,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                                lineNumber: 316,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium mb-2\",\n                                                                        children: [\n                                                                            \"Style: \",\n                                                                            settings.style.toFixed(2)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                                        lineNumber: 332,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"range\",\n                                                                        min: \"0\",\n                                                                        max: \"1\",\n                                                                        step: \"0.01\",\n                                                                        value: settings.style,\n                                                                        onChange: (e)=>setSettings((prev)=>({\n                                                                                    ...prev,\n                                                                                    style: parseFloat(e.target.value)\n                                                                                })),\n                                                                        className: \"w-full\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                                        lineNumber: 335,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                                lineNumber: 331,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"checkbox\",\n                                                                        id: \"speaker-boost\",\n                                                                        checked: settings.use_speaker_boost,\n                                                                        onChange: (e)=>setSettings((prev)=>({\n                                                                                    ...prev,\n                                                                                    use_speaker_boost: e.target.checked\n                                                                                })),\n                                                                        className: \"rounded\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                                        lineNumber: 347,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        htmlFor: \"speaker-boost\",\n                                                                        className: \"text-sm\",\n                                                                        children: \"Speaker Boost\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                                        lineNumber: 354,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                                lineNumber: 346,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                        lineNumber: 296,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                lineNumber: 286,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                        lineNumber: 246,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                    lineNumber: 245,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"lg:col-span-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"card\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-xl font-semibold mb-4\",\n                                                children: \"Enter Text\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                lineNumber: 367,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                value: text,\n                                                onChange: (e)=>setText(e.target.value),\n                                                placeholder: \"Type the text you want to convert to speech...\",\n                                                className: \"input-field w-full h-40 resize-none mb-4\",\n                                                maxLength: 5000\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                lineNumber: 369,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-center mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-400\",\n                                                        children: [\n                                                            text.length,\n                                                            \"/5000 characters\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                        lineNumber: 378,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-400\",\n                                                        children: [\n                                                            \"Estimated: ~\",\n                                                            Math.ceil(text.length / 10),\n                                                            \" seconds\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                        lineNumber: 381,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                lineNumber: 377,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"text-sm font-medium mb-3\",\n                                                        children: \"Quick Samples:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                        lineNumber: 388,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-2\",\n                                                        children: sampleTexts.map((sample, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>setText(sample),\n                                                                className: \"text-left p-3 rounded-lg border border-gray-700 hover:border-gray-600 transition-colors text-sm\",\n                                                                children: [\n                                                                    '\"',\n                                                                    sample.substring(0, 50),\n                                                                    '...\"'\n                                                                ]\n                                                            }, index, true, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                                lineNumber: 391,\n                                                                columnNumber: 23\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                        lineNumber: 389,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                lineNumber: 387,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: generateSpeech,\n                                                disabled: !selectedVoice || !text.trim() || isGenerating || !coquiServerReady,\n                                                className: \"btn-primary w-full flex items-center justify-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                                children: isGenerating ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"animate-spin rounded-full h-5 w-5 border-b-2 border-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                            lineNumber: 410,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Generating with Coqui TTS...\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                            lineNumber: 411,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true) : !coquiServerReady ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Download_Mic_Pause_Play_Settings_Sparkles_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                            className: \"w-5 h-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                            lineNumber: 415,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Start Coqui Server First\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                            lineNumber: 416,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Download_Mic_Pause_Play_Settings_Sparkles_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                            className: \"w-5 h-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                            lineNumber: 420,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Generate Speech\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                            lineNumber: 421,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                lineNumber: 403,\n                                                columnNumber: 17\n                                            }, this),\n                                            !coquiServerReady && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-yellow-400 text-center\",\n                                                children: \"⚠️ Coqui TTS server is required for speech generation\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                lineNumber: 427,\n                                                columnNumber: 19\n                                            }, this),\n                                            audioUrl && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                                initial: {\n                                                    opacity: 0,\n                                                    y: 20\n                                                },\n                                                animate: {\n                                                    opacity: 1,\n                                                    y: 0\n                                                },\n                                                className: \"mt-6 p-4 bg-gray-800/50 rounded-lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: playAudio,\n                                                                        className: \"btn-secondary flex items-center space-x-2\",\n                                                                        children: [\n                                                                            isPlaying ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Download_Mic_Pause_Play_Settings_Sparkles_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                                className: \"w-4 h-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                                                lineNumber: 446,\n                                                                                columnNumber: 29\n                                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Download_Mic_Pause_Play_Settings_Sparkles_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                                className: \"w-4 h-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                                                lineNumber: 448,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: isPlaying ? 'Pause' : 'Play'\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                                                lineNumber: 450,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                                        lineNumber: 441,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm text-gray-400\",\n                                                                        children: \"Generated speech ready\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                                        lineNumber: 453,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                                lineNumber: 440,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: downloadAudio,\n                                                                className: \"btn-secondary flex items-center space-x-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Download_Mic_Pause_Play_Settings_Sparkles_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                        className: \"w-4 h-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                                        lineNumber: 462,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"Download\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                                        lineNumber: 463,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                                lineNumber: 458,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                        lineNumber: 439,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"audio\", {\n                                                        ref: audioRef,\n                                                        src: audioUrl,\n                                                        onEnded: ()=>setIsPlaying(false),\n                                                        onPlay: ()=>setIsPlaying(true),\n                                                        onPause: ()=>setIsPlaying(false),\n                                                        className: \"hidden\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                        lineNumber: 467,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                lineNumber: 434,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                        lineNumber: 366,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                    lineNumber: 365,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                            lineNumber: 243,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                    lineNumber: 198,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                lineNumber: 197,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n        lineNumber: 180,\n        columnNumber: 5\n    }, this);\n}\n_s(GeneratePage, \"xi8DBwhXfk11PN4jc/wJb+CBEaw=\");\n_c = GeneratePage;\nvar _c;\n$RefreshReg$(_c, \"GeneratePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/generate/page.tsx\n"));

/***/ })

});