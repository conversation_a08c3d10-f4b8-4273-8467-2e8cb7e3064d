"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/generate/page",{

/***/ "(app-pages-browser)/./src/app/generate/page.tsx":
/*!***********************************!*\
  !*** ./src/app/generate/page.tsx ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ GeneratePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Download_Mic_Pause_Play_Settings_Sparkles_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Download,Mic,Pause,Play,Settings,Sparkles,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Download_Mic_Pause_Play_Settings_Sparkles_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Download,Mic,Pause,Play,Settings,Sparkles,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Download_Mic_Pause_Play_Settings_Sparkles_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Download,Mic,Pause,Play,Settings,Sparkles,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mic.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Download_Mic_Pause_Play_Settings_Sparkles_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Download,Mic,Pause,Play,Settings,Sparkles,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Download_Mic_Pause_Play_Settings_Sparkles_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Download,Mic,Pause,Play,Settings,Sparkles,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/volume-2.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Download_Mic_Pause_Play_Settings_Sparkles_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Download,Mic,Pause,Play,Settings,Sparkles,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pause.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Download_Mic_Pause_Play_Settings_Sparkles_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Download,Mic,Pause,Play,Settings,Sparkles,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Download_Mic_Pause_Play_Settings_Sparkles_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Download,Mic,Pause,Play,Settings,Sparkles,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _lib_localStorage__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/localStorage */ \"(app-pages-browser)/./src/lib/localStorage.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction GeneratePage() {\n    _s();\n    const [voiceModels, setVoiceModels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedVoice, setSelectedVoice] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [text, setText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isGenerating, setIsGenerating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isPlaying, setIsPlaying] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [audioUrl, setAudioUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showSettings, setShowSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [settings, setSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        stability: 0.5,\n        similarity_boost: 0.5,\n        style: 0.0,\n        use_speaker_boost: true\n    });\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const audioRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"GeneratePage.useEffect\": ()=>{\n            fetchVoiceModels();\n        }\n    }[\"GeneratePage.useEffect\"], []);\n    const fetchVoiceModels = async ()=>{\n        try {\n            const response = await fetch('/api/voice-clone');\n            const data = await response.json();\n            if (data.success) {\n                let allVoiceModels = data.voiceModels || [];\n                // Also check localStorage for voice models (fallback)\n                const localModels = _lib_localStorage__WEBPACK_IMPORTED_MODULE_3__.localStorageService.getVoiceModels();\n                if (localModels.length > 0) {\n                    // Convert local models to the expected format\n                    const convertedLocalModels = localModels.map((model)=>({\n                            ...model,\n                            updated_at: model.created_at\n                        }));\n                    // Merge with API models, avoiding duplicates\n                    const existingVoiceIds = allVoiceModels.map((m)=>m.voice_id);\n                    const newLocalModels = convertedLocalModels.filter((model)=>!existingVoiceIds.includes(model.voice_id));\n                    allVoiceModels = [\n                        ...allVoiceModels,\n                        ...newLocalModels\n                    ];\n                }\n                setVoiceModels(allVoiceModels);\n                if (allVoiceModels.length > 0) {\n                    setSelectedVoice(allVoiceModels[0]);\n                }\n            }\n        } catch (error) {\n            console.error('Failed to fetch voice models:', error);\n            // Fallback to localStorage only\n            const localModels = _lib_localStorage__WEBPACK_IMPORTED_MODULE_3__.localStorageService.getVoiceModels();\n            if (localModels.length > 0) {\n                const convertedLocalModels = localModels.map((model)=>({\n                        ...model,\n                        updated_at: model.created_at\n                    }));\n                setVoiceModels(convertedLocalModels);\n                setSelectedVoice(convertedLocalModels[0]);\n            } else {\n                setError('Failed to load voice models');\n            }\n        }\n    };\n    const generateSpeech = async ()=>{\n        if (!selectedVoice || !text.trim()) return;\n        setIsGenerating(true);\n        setError(null);\n        try {\n            const response = await fetch('/api/generate-speech', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    text: text.trim(),\n                    voiceModelId: selectedVoice.id,\n                    settings\n                })\n            });\n            if (!response.ok) {\n                const errorData = await response.json();\n                throw new Error(errorData.error || 'Failed to generate speech');\n            }\n            // Get audio blob from response\n            const audioBlob = await response.blob();\n            // Clean up previous audio URL\n            if (audioUrl) {\n                URL.revokeObjectURL(audioUrl);\n            }\n            // Create new audio URL\n            const newAudioUrl = URL.createObjectURL(audioBlob);\n            setAudioUrl(newAudioUrl);\n        } catch (error) {\n            console.error('Speech generation failed:', error);\n            setError(error instanceof Error ? error.message : 'Failed to generate speech');\n        } finally{\n            setIsGenerating(false);\n        }\n    };\n    const playAudio = ()=>{\n        if (!audioRef.current || !audioUrl) return;\n        if (isPlaying) {\n            audioRef.current.pause();\n            setIsPlaying(false);\n        } else {\n            audioRef.current.play();\n            setIsPlaying(true);\n        }\n    };\n    const downloadAudio = ()=>{\n        if (!audioUrl) return;\n        const a = document.createElement('a');\n        a.href = audioUrl;\n        a.download = \"voice-clone-\".concat(Date.now(), \".mp3\");\n        document.body.appendChild(a);\n        a.click();\n        document.body.removeChild(a);\n    };\n    const sampleTexts = [\n        \"Hello, this is my AI voice clone speaking. How do I sound?\",\n        \"Welcome to the future of voice technology. Your voice, unlimited possibilities.\",\n        \"I can read any text you give me in your own voice. Pretty amazing, right?\",\n        \"The quick brown fox jumps over the lazy dog. This sentence contains every letter of the alphabet.\"\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen py-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"fixed top-0 w-full z-50 bg-black/80 backdrop-blur-md border-b border-gray-800\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center h-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/\",\n                                className: \"flex items-center space-x-2 text-gray-300 hover:text-white transition-colors\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Download_Mic_Pause_Play_Settings_Sparkles_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                        lineNumber: 176,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Back to Home\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                        lineNumber: 177,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                lineNumber: 175,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Download_Mic_Pause_Play_Settings_Sparkles_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"w-8 h-8 text-purple-400\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                        lineNumber: 180,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xl font-bold gradient-text\",\n                                        children: \"VoiceClone AI\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                        lineNumber: 181,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                lineNumber: 179,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                        lineNumber: 174,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                    lineNumber: 173,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                lineNumber: 172,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"pt-24 px-4 sm:px-6 lg:px-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-6xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-4xl md:text-5xl font-bold mb-4\",\n                                    children: [\n                                        \"Generate \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"gradient-text\",\n                                            children: \"Speech\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                            lineNumber: 191,\n                                            columnNumber: 24\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                    lineNumber: 190,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl text-gray-300\",\n                                    children: \"Type any text and hear it spoken in your cloned voice\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                    lineNumber: 193,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                            lineNumber: 189,\n                            columnNumber: 11\n                        }, this),\n                        error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: -10\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            className: \"bg-red-900/50 border border-red-700 rounded-lg p-4 text-red-200 mb-6 max-w-2xl mx-auto\",\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                            lineNumber: 199,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid lg:grid-cols-3 gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"lg:col-span-1\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"card\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-xl font-semibold mb-4 flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Download_Mic_Pause_Play_Settings_Sparkles_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        className: \"w-5 h-5 mr-2 text-purple-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                        lineNumber: 213,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Select Voice\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                lineNumber: 212,\n                                                columnNumber: 17\n                                            }, this),\n                                            voiceModels.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center py-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-400 mb-4\",\n                                                        children: \"No voice models found\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                        lineNumber: 219,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: \"/record\",\n                                                        className: \"btn-primary\",\n                                                        children: \"Create Voice Clone\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                        lineNumber: 220,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                lineNumber: 218,\n                                                columnNumber: 19\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: voiceModels.map((voice)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setSelectedVoice(voice),\n                                                        className: \"w-full p-4 rounded-lg border transition-all duration-200 text-left \".concat((selectedVoice === null || selectedVoice === void 0 ? void 0 : selectedVoice.id) === voice.id ? 'border-purple-500 bg-purple-900/20' : 'border-gray-700 hover:border-gray-600'),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"font-medium\",\n                                                                children: voice.name\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                                lineNumber: 236,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            voice.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-gray-400 mt-1\",\n                                                                children: voice.description\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                                lineNumber: 238,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-gray-500 mt-2\",\n                                                                children: [\n                                                                    \"Created \",\n                                                                    new Date(voice.created_at).toLocaleDateString()\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                                lineNumber: 242,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, voice.id, true, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                        lineNumber: 227,\n                                                        columnNumber: 23\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                lineNumber: 225,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setShowSettings(!showSettings),\n                                                        className: \"btn-secondary w-full flex items-center justify-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Download_Mic_Pause_Play_Settings_Sparkles_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                className: \"w-4 h-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                                lineNumber: 256,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"Voice Settings\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                                lineNumber: 257,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                        lineNumber: 252,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    showSettings && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                                        initial: {\n                                                            opacity: 0,\n                                                            height: 0\n                                                        },\n                                                        animate: {\n                                                            opacity: 1,\n                                                            height: 'auto'\n                                                        },\n                                                        className: \"mt-4 space-y-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium mb-2\",\n                                                                        children: [\n                                                                            \"Stability: \",\n                                                                            settings.stability.toFixed(2)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                                        lineNumber: 267,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"range\",\n                                                                        min: \"0\",\n                                                                        max: \"1\",\n                                                                        step: \"0.01\",\n                                                                        value: settings.stability,\n                                                                        onChange: (e)=>setSettings((prev)=>({\n                                                                                    ...prev,\n                                                                                    stability: parseFloat(e.target.value)\n                                                                                })),\n                                                                        className: \"w-full\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                                        lineNumber: 270,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                                lineNumber: 266,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium mb-2\",\n                                                                        children: [\n                                                                            \"Similarity: \",\n                                                                            settings.similarity_boost.toFixed(2)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                                        lineNumber: 282,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"range\",\n                                                                        min: \"0\",\n                                                                        max: \"1\",\n                                                                        step: \"0.01\",\n                                                                        value: settings.similarity_boost,\n                                                                        onChange: (e)=>setSettings((prev)=>({\n                                                                                    ...prev,\n                                                                                    similarity_boost: parseFloat(e.target.value)\n                                                                                })),\n                                                                        className: \"w-full\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                                        lineNumber: 285,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                                lineNumber: 281,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium mb-2\",\n                                                                        children: [\n                                                                            \"Style: \",\n                                                                            settings.style.toFixed(2)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                                        lineNumber: 297,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"range\",\n                                                                        min: \"0\",\n                                                                        max: \"1\",\n                                                                        step: \"0.01\",\n                                                                        value: settings.style,\n                                                                        onChange: (e)=>setSettings((prev)=>({\n                                                                                    ...prev,\n                                                                                    style: parseFloat(e.target.value)\n                                                                                })),\n                                                                        className: \"w-full\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                                        lineNumber: 300,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                                lineNumber: 296,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"checkbox\",\n                                                                        id: \"speaker-boost\",\n                                                                        checked: settings.use_speaker_boost,\n                                                                        onChange: (e)=>setSettings((prev)=>({\n                                                                                    ...prev,\n                                                                                    use_speaker_boost: e.target.checked\n                                                                                })),\n                                                                        className: \"rounded\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                                        lineNumber: 312,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        htmlFor: \"speaker-boost\",\n                                                                        className: \"text-sm\",\n                                                                        children: \"Speaker Boost\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                                        lineNumber: 319,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                                lineNumber: 311,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                        lineNumber: 261,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                lineNumber: 251,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                        lineNumber: 211,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                    lineNumber: 210,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"lg:col-span-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"card\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-xl font-semibold mb-4\",\n                                                children: \"Enter Text\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                lineNumber: 332,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                value: text,\n                                                onChange: (e)=>setText(e.target.value),\n                                                placeholder: \"Type the text you want to convert to speech...\",\n                                                className: \"input-field w-full h-40 resize-none mb-4\",\n                                                maxLength: 5000\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                lineNumber: 334,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-center mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-400\",\n                                                        children: [\n                                                            text.length,\n                                                            \"/5000 characters\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                        lineNumber: 343,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-400\",\n                                                        children: [\n                                                            \"Estimated: ~\",\n                                                            Math.ceil(text.length / 10),\n                                                            \" seconds\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                        lineNumber: 346,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                lineNumber: 342,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"text-sm font-medium mb-3\",\n                                                        children: \"Quick Samples:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                        lineNumber: 353,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-2\",\n                                                        children: sampleTexts.map((sample, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>setText(sample),\n                                                                className: \"text-left p-3 rounded-lg border border-gray-700 hover:border-gray-600 transition-colors text-sm\",\n                                                                children: [\n                                                                    '\"',\n                                                                    sample.substring(0, 50),\n                                                                    '...\"'\n                                                                ]\n                                                            }, index, true, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                                lineNumber: 356,\n                                                                columnNumber: 23\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                        lineNumber: 354,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                lineNumber: 352,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: generateSpeech,\n                                                disabled: !selectedVoice || !text.trim() || isGenerating,\n                                                className: \"btn-primary w-full flex items-center justify-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                                children: isGenerating ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"animate-spin rounded-full h-5 w-5 border-b-2 border-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                            lineNumber: 375,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Generating...\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                            lineNumber: 376,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Download_Mic_Pause_Play_Settings_Sparkles_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                            className: \"w-5 h-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                            lineNumber: 380,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Generate Speech\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                            lineNumber: 381,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                lineNumber: 368,\n                                                columnNumber: 17\n                                            }, this),\n                                            audioUrl && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                                initial: {\n                                                    opacity: 0,\n                                                    y: 20\n                                                },\n                                                animate: {\n                                                    opacity: 1,\n                                                    y: 0\n                                                },\n                                                className: \"mt-6 p-4 bg-gray-800/50 rounded-lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: playAudio,\n                                                                        className: \"btn-secondary flex items-center space-x-2\",\n                                                                        children: [\n                                                                            isPlaying ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Download_Mic_Pause_Play_Settings_Sparkles_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                                className: \"w-4 h-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                                                lineNumber: 400,\n                                                                                columnNumber: 29\n                                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Download_Mic_Pause_Play_Settings_Sparkles_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                                className: \"w-4 h-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                                                lineNumber: 402,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: isPlaying ? 'Pause' : 'Play'\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                                                lineNumber: 404,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                                        lineNumber: 395,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm text-gray-400\",\n                                                                        children: \"Generated speech ready\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                                        lineNumber: 407,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                                lineNumber: 394,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: downloadAudio,\n                                                                className: \"btn-secondary flex items-center space-x-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Download_Mic_Pause_Play_Settings_Sparkles_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                        className: \"w-4 h-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                                        lineNumber: 416,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"Download\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                                        lineNumber: 417,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                                lineNumber: 412,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                        lineNumber: 393,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"audio\", {\n                                                        ref: audioRef,\n                                                        src: audioUrl,\n                                                        onEnded: ()=>setIsPlaying(false),\n                                                        onPlay: ()=>setIsPlaying(true),\n                                                        onPause: ()=>setIsPlaying(false),\n                                                        className: \"hidden\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                        lineNumber: 421,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                lineNumber: 388,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                        lineNumber: 331,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                    lineNumber: 330,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                            lineNumber: 208,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                    lineNumber: 188,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                lineNumber: 187,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n        lineNumber: 170,\n        columnNumber: 5\n    }, this);\n}\n_s(GeneratePage, \"rt3COj6wkSejPErkJouH3vkcJ7A=\");\n_c = GeneratePage;\nvar _c;\n$RefreshReg$(_c, \"GeneratePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/generate/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/localStorage.ts":
/*!*********************************!*\
  !*** ./src/lib/localStorage.ts ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   localStorageService: () => (/* binding */ localStorageService)\n/* harmony export */ });\n// Local storage utilities for voice models\n// This serves as a fallback when Supabase is not available\nconst VOICE_MODELS_KEY = 'voice_clone_models';\nconst localStorageService = {\n    // Get all voice models from localStorage\n    getVoiceModels () {\n        if (false) {}\n        try {\n            const stored = localStorage.getItem(VOICE_MODELS_KEY);\n            return stored ? JSON.parse(stored) : [];\n        } catch (error) {\n            console.error('Error reading voice models from localStorage:', error);\n            return [];\n        }\n    },\n    // Save a voice model to localStorage\n    saveVoiceModel (voiceModel) {\n        if (false) {}\n        try {\n            const existing = this.getVoiceModels();\n            const updated = [\n                ...existing,\n                voiceModel\n            ];\n            localStorage.setItem(VOICE_MODELS_KEY, JSON.stringify(updated));\n        } catch (error) {\n            console.error('Error saving voice model to localStorage:', error);\n        }\n    },\n    // Remove a voice model from localStorage\n    removeVoiceModel (voiceId) {\n        if (false) {}\n        try {\n            const existing = this.getVoiceModels();\n            const filtered = existing.filter((model)=>model.voice_id !== voiceId);\n            localStorage.setItem(VOICE_MODELS_KEY, JSON.stringify(filtered));\n        } catch (error) {\n            console.error('Error removing voice model from localStorage:', error);\n        }\n    },\n    // Clear all voice models\n    clearVoiceModels () {\n        if (false) {}\n        try {\n            localStorage.removeItem(VOICE_MODELS_KEY);\n        } catch (error) {\n            console.error('Error clearing voice models from localStorage:', error);\n        }\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/localStorage.ts\n"));

/***/ })

});