"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/generate/page",{

/***/ "(app-pages-browser)/./src/app/generate/page.tsx":
/*!***********************************!*\
  !*** ./src/app/generate/page.tsx ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ GeneratePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Download_Mic_Pause_Play_Settings_Sparkles_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Download,Mic,Pause,Play,Settings,Sparkles,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Download_Mic_Pause_Play_Settings_Sparkles_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Download,Mic,Pause,Play,Settings,Sparkles,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Download_Mic_Pause_Play_Settings_Sparkles_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Download,Mic,Pause,Play,Settings,Sparkles,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mic.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Download_Mic_Pause_Play_Settings_Sparkles_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Download,Mic,Pause,Play,Settings,Sparkles,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Download_Mic_Pause_Play_Settings_Sparkles_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Download,Mic,Pause,Play,Settings,Sparkles,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/volume-2.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Download_Mic_Pause_Play_Settings_Sparkles_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Download,Mic,Pause,Play,Settings,Sparkles,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pause.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Download_Mic_Pause_Play_Settings_Sparkles_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Download,Mic,Pause,Play,Settings,Sparkles,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Download_Mic_Pause_Play_Settings_Sparkles_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Download,Mic,Pause,Play,Settings,Sparkles,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _lib_localStorage__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/localStorage */ \"(app-pages-browser)/./src/lib/localStorage.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction GeneratePage() {\n    _s();\n    const [voiceModels, setVoiceModels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedVoice, setSelectedVoice] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [text, setText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isGenerating, setIsGenerating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isPlaying, setIsPlaying] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [audioUrl, setAudioUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showSettings, setShowSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [settings, setSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        stability: 0.5,\n        similarity_boost: 0.5,\n        style: 0.0,\n        use_speaker_boost: true\n    });\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [coquiServerReady, setCoquiServerReady] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const audioRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"GeneratePage.useEffect\": ()=>{\n            fetchVoiceModels();\n        }\n    }[\"GeneratePage.useEffect\"], []);\n    const fetchVoiceModels = async ()=>{\n        try {\n            // Check if Coqui TTS server is running\n            let serverRunning = false;\n            try {\n                const response = await fetch('http://localhost:5002', {\n                    method: 'GET'\n                });\n                serverRunning = response.ok;\n                setCoquiServerReady(serverRunning);\n            } catch (error) {\n                console.log('Coqui TTS server not running');\n                setCoquiServerReady(false);\n            }\n            // Get voice models from localStorage\n            const localModels = _lib_localStorage__WEBPACK_IMPORTED_MODULE_3__.localStorageService.getVoiceModels();\n            let allVoiceModels = [];\n            if (localModels.length > 0) {\n                // Convert local models to the expected format\n                allVoiceModels = localModels.map((model)=>({\n                        ...model,\n                        updated_at: model.created_at\n                    }));\n            } else {\n                // Create a demo voice model if none exist\n                allVoiceModels = [\n                    {\n                        id: 'demo-voice-1',\n                        name: 'Demo Voice (Coqui TTS)',\n                        description: 'Default Coqui TTS voice using Tacotron2',\n                        voice_id: 'coqui-demo',\n                        created_at: new Date().toISOString()\n                    }\n                ];\n            }\n            setVoiceModels(allVoiceModels);\n            if (allVoiceModels.length > 0) {\n                setSelectedVoice(allVoiceModels[0]);\n            }\n        } catch (error) {\n            console.error('Failed to fetch voice models:', error);\n            setError('Failed to load voice models');\n        }\n    };\n    const generateSpeech = async ()=>{\n        if (!selectedVoice || !text.trim()) return;\n        setIsGenerating(true);\n        setError(null);\n        try {\n            // Call our API route which proxies to Coqui TTS\n            console.log('Generating speech with text:', text.trim());\n            const response = await fetch('/api/coqui-generate-speech', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    text: text.trim(),\n                    voiceModelId: selectedVoice.id,\n                    settings: {\n                        speed: settings.stability || 1.0,\n                        language: 'en',\n                        model: 'tts_models/en/ljspeech/tacotron2-DDC'\n                    }\n                })\n            });\n            if (!response.ok) {\n                const errorData = await response.json();\n                throw new Error(errorData.error || errorData.message || 'Failed to generate speech');\n            }\n            // Get audio blob from response\n            const audioBlob = await response.blob();\n            // Clean up previous audio URL\n            if (audioUrl) {\n                URL.revokeObjectURL(audioUrl);\n            }\n            // Create new audio URL\n            const newAudioUrl = URL.createObjectURL(audioBlob);\n            setAudioUrl(newAudioUrl);\n            console.log('Speech generated successfully!');\n        } catch (error) {\n            console.error('Speech generation failed:', error);\n            setError(error instanceof Error ? error.message : 'Failed to generate speech');\n        } finally{\n            setIsGenerating(false);\n        }\n    };\n    const playAudio = ()=>{\n        if (!audioRef.current || !audioUrl) return;\n        if (isPlaying) {\n            audioRef.current.pause();\n            setIsPlaying(false);\n        } else {\n            audioRef.current.play();\n            setIsPlaying(true);\n        }\n    };\n    const downloadAudio = ()=>{\n        if (!audioUrl) return;\n        const a = document.createElement('a');\n        a.href = audioUrl;\n        a.download = \"voice-clone-\".concat(Date.now(), \".mp3\");\n        document.body.appendChild(a);\n        a.click();\n        document.body.removeChild(a);\n    };\n    const sampleTexts = [\n        \"Hello, this is my AI voice clone speaking. How do I sound?\",\n        \"Welcome to the future of voice technology. Your voice, unlimited possibilities.\",\n        \"I can read any text you give me in your own voice. Pretty amazing, right?\",\n        \"The quick brown fox jumps over the lazy dog. This sentence contains every letter of the alphabet.\"\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen py-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"fixed top-0 w-full z-50 bg-black/80 backdrop-blur-md border-b border-gray-800\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center h-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/\",\n                                className: \"flex items-center space-x-2 text-gray-300 hover:text-white transition-colors\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Download_Mic_Pause_Play_Settings_Sparkles_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                        lineNumber: 182,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Back to Home\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                        lineNumber: 183,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                lineNumber: 181,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Download_Mic_Pause_Play_Settings_Sparkles_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"w-8 h-8 text-purple-400\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                        lineNumber: 186,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xl font-bold gradient-text\",\n                                        children: \"VoiceClone AI\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                        lineNumber: 187,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                lineNumber: 185,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                        lineNumber: 180,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                    lineNumber: 179,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                lineNumber: 178,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"pt-24 px-4 sm:px-6 lg:px-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-6xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-4xl md:text-5xl font-bold mb-4\",\n                                    children: [\n                                        \"Generate \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"gradient-text\",\n                                            children: \"Speech\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                            lineNumber: 197,\n                                            columnNumber: 24\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                    lineNumber: 196,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl text-gray-300 mb-4\",\n                                    children: \"Type any text and hear it spoken in your cloned voice using Coqui TTS\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                    lineNumber: 199,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"inline-flex items-center px-4 py-2 bg-blue-900/30 border border-blue-700 rounded-lg text-blue-300\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm\",\n                                        children: \"\\uD83D\\uDE80 Powered by Coqui TTS - Free & Open Source\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                        lineNumber: 203,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                    lineNumber: 202,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                            lineNumber: 195,\n                            columnNumber: 11\n                        }, this),\n                        error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: -10\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            className: \"bg-red-900/50 border border-red-700 rounded-lg p-4 text-red-200 mb-6 max-w-2xl mx-auto\",\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                            lineNumber: 208,\n                            columnNumber: 13\n                        }, this),\n                        !coquiServerReady && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"card\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xl font-semibold mb-4\",\n                                        children: \"Coqui TTS Server Required\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                        lineNumber: 221,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-300 mb-4\",\n                                        children: [\n                                            \"To use voice cloning, you need to start the Coqui TTS server. The server is currently \",\n                                            coquiServerReady ? 'running' : 'not running',\n                                            \".\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                        lineNumber: 222,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>{\n                                            setCoquiServerReady(true);\n                                            fetchVoiceModels();\n                                        },\n                                        className: \"btn-primary\",\n                                        children: \"Mark Server as Ready\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                        lineNumber: 226,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                lineNumber: 220,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                            lineNumber: 219,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid lg:grid-cols-3 gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"lg:col-span-1\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"card\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-xl font-semibold mb-4 flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Download_Mic_Pause_Play_Settings_Sparkles_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        className: \"w-5 h-5 mr-2 text-purple-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                        lineNumber: 244,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Select Voice\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                lineNumber: 243,\n                                                columnNumber: 17\n                                            }, this),\n                                            voiceModels.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center py-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-400 mb-4\",\n                                                        children: \"No voice models found\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                        lineNumber: 250,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: \"/record\",\n                                                        className: \"btn-primary\",\n                                                        children: \"Create Voice Clone\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                        lineNumber: 251,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                lineNumber: 249,\n                                                columnNumber: 19\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: voiceModels.map((voice)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setSelectedVoice(voice),\n                                                        className: \"w-full p-4 rounded-lg border transition-all duration-200 text-left \".concat((selectedVoice === null || selectedVoice === void 0 ? void 0 : selectedVoice.id) === voice.id ? 'border-purple-500 bg-purple-900/20' : 'border-gray-700 hover:border-gray-600'),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"font-medium\",\n                                                                children: voice.name\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                                lineNumber: 267,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            voice.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-gray-400 mt-1\",\n                                                                children: voice.description\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                                lineNumber: 269,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-gray-500 mt-2\",\n                                                                children: [\n                                                                    \"Created \",\n                                                                    new Date(voice.created_at).toLocaleDateString()\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                                lineNumber: 273,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, voice.id, true, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                        lineNumber: 258,\n                                                        columnNumber: 23\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                lineNumber: 256,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setShowSettings(!showSettings),\n                                                        className: \"btn-secondary w-full flex items-center justify-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Download_Mic_Pause_Play_Settings_Sparkles_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                className: \"w-4 h-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                                lineNumber: 287,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"Voice Settings\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                                lineNumber: 288,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                        lineNumber: 283,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    showSettings && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                                        initial: {\n                                                            opacity: 0,\n                                                            height: 0\n                                                        },\n                                                        animate: {\n                                                            opacity: 1,\n                                                            height: 'auto'\n                                                        },\n                                                        className: \"mt-4 space-y-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium mb-2\",\n                                                                        children: [\n                                                                            \"Stability: \",\n                                                                            settings.stability.toFixed(2)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                                        lineNumber: 298,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"range\",\n                                                                        min: \"0\",\n                                                                        max: \"1\",\n                                                                        step: \"0.01\",\n                                                                        value: settings.stability,\n                                                                        onChange: (e)=>setSettings((prev)=>({\n                                                                                    ...prev,\n                                                                                    stability: parseFloat(e.target.value)\n                                                                                })),\n                                                                        className: \"w-full\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                                        lineNumber: 301,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                                lineNumber: 297,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium mb-2\",\n                                                                        children: [\n                                                                            \"Similarity: \",\n                                                                            settings.similarity_boost.toFixed(2)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                                        lineNumber: 313,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"range\",\n                                                                        min: \"0\",\n                                                                        max: \"1\",\n                                                                        step: \"0.01\",\n                                                                        value: settings.similarity_boost,\n                                                                        onChange: (e)=>setSettings((prev)=>({\n                                                                                    ...prev,\n                                                                                    similarity_boost: parseFloat(e.target.value)\n                                                                                })),\n                                                                        className: \"w-full\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                                        lineNumber: 316,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                                lineNumber: 312,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium mb-2\",\n                                                                        children: [\n                                                                            \"Style: \",\n                                                                            settings.style.toFixed(2)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                                        lineNumber: 328,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"range\",\n                                                                        min: \"0\",\n                                                                        max: \"1\",\n                                                                        step: \"0.01\",\n                                                                        value: settings.style,\n                                                                        onChange: (e)=>setSettings((prev)=>({\n                                                                                    ...prev,\n                                                                                    style: parseFloat(e.target.value)\n                                                                                })),\n                                                                        className: \"w-full\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                                        lineNumber: 331,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                                lineNumber: 327,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"checkbox\",\n                                                                        id: \"speaker-boost\",\n                                                                        checked: settings.use_speaker_boost,\n                                                                        onChange: (e)=>setSettings((prev)=>({\n                                                                                    ...prev,\n                                                                                    use_speaker_boost: e.target.checked\n                                                                                })),\n                                                                        className: \"rounded\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                                        lineNumber: 343,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        htmlFor: \"speaker-boost\",\n                                                                        className: \"text-sm\",\n                                                                        children: \"Speaker Boost\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                                        lineNumber: 350,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                                lineNumber: 342,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                        lineNumber: 292,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                lineNumber: 282,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                        lineNumber: 242,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                    lineNumber: 241,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"lg:col-span-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"card\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-xl font-semibold mb-4\",\n                                                children: \"Enter Text\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                lineNumber: 363,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                value: text,\n                                                onChange: (e)=>setText(e.target.value),\n                                                placeholder: \"Type the text you want to convert to speech...\",\n                                                className: \"input-field w-full h-40 resize-none mb-4\",\n                                                maxLength: 5000\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                lineNumber: 365,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-center mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-400\",\n                                                        children: [\n                                                            text.length,\n                                                            \"/5000 characters\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                        lineNumber: 374,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-400\",\n                                                        children: [\n                                                            \"Estimated: ~\",\n                                                            Math.ceil(text.length / 10),\n                                                            \" seconds\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                        lineNumber: 377,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                lineNumber: 373,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"text-sm font-medium mb-3\",\n                                                        children: \"Quick Samples:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                        lineNumber: 384,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-2\",\n                                                        children: sampleTexts.map((sample, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>setText(sample),\n                                                                className: \"text-left p-3 rounded-lg border border-gray-700 hover:border-gray-600 transition-colors text-sm\",\n                                                                children: [\n                                                                    '\"',\n                                                                    sample.substring(0, 50),\n                                                                    '...\"'\n                                                                ]\n                                                            }, index, true, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                                lineNumber: 387,\n                                                                columnNumber: 23\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                        lineNumber: 385,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                lineNumber: 383,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: generateSpeech,\n                                                disabled: !selectedVoice || !text.trim() || isGenerating || !coquiServerReady,\n                                                className: \"btn-primary w-full flex items-center justify-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                                children: isGenerating ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"animate-spin rounded-full h-5 w-5 border-b-2 border-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                            lineNumber: 406,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Generating with Coqui TTS...\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                            lineNumber: 407,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true) : !coquiServerReady ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Download_Mic_Pause_Play_Settings_Sparkles_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                            className: \"w-5 h-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                            lineNumber: 411,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Start Coqui Server First\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                            lineNumber: 412,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Download_Mic_Pause_Play_Settings_Sparkles_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                            className: \"w-5 h-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                            lineNumber: 416,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Generate Speech\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                            lineNumber: 417,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                lineNumber: 399,\n                                                columnNumber: 17\n                                            }, this),\n                                            !coquiServerReady && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-yellow-400 text-center\",\n                                                children: \"⚠️ Coqui TTS server is required for speech generation\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                lineNumber: 423,\n                                                columnNumber: 19\n                                            }, this),\n                                            audioUrl && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                                initial: {\n                                                    opacity: 0,\n                                                    y: 20\n                                                },\n                                                animate: {\n                                                    opacity: 1,\n                                                    y: 0\n                                                },\n                                                className: \"mt-6 p-4 bg-gray-800/50 rounded-lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: playAudio,\n                                                                        className: \"btn-secondary flex items-center space-x-2\",\n                                                                        children: [\n                                                                            isPlaying ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Download_Mic_Pause_Play_Settings_Sparkles_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                                className: \"w-4 h-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                                                lineNumber: 442,\n                                                                                columnNumber: 29\n                                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Download_Mic_Pause_Play_Settings_Sparkles_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                                className: \"w-4 h-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                                                lineNumber: 444,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: isPlaying ? 'Pause' : 'Play'\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                                                lineNumber: 446,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                                        lineNumber: 437,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm text-gray-400\",\n                                                                        children: \"Generated speech ready\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                                        lineNumber: 449,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                                lineNumber: 436,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: downloadAudio,\n                                                                className: \"btn-secondary flex items-center space-x-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Download_Mic_Pause_Play_Settings_Sparkles_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                        className: \"w-4 h-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                                        lineNumber: 458,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"Download\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                                        lineNumber: 459,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                                lineNumber: 454,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                        lineNumber: 435,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"audio\", {\n                                                        ref: audioRef,\n                                                        src: audioUrl,\n                                                        onEnded: ()=>setIsPlaying(false),\n                                                        onPlay: ()=>setIsPlaying(true),\n                                                        onPause: ()=>setIsPlaying(false),\n                                                        className: \"hidden\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                        lineNumber: 463,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                lineNumber: 430,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                        lineNumber: 362,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                    lineNumber: 361,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                            lineNumber: 239,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                    lineNumber: 194,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                lineNumber: 193,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n        lineNumber: 176,\n        columnNumber: 5\n    }, this);\n}\n_s(GeneratePage, \"xi8DBwhXfk11PN4jc/wJb+CBEaw=\");\n_c = GeneratePage;\nvar _c;\n$RefreshReg$(_c, \"GeneratePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/generate/page.tsx\n"));

/***/ })

});