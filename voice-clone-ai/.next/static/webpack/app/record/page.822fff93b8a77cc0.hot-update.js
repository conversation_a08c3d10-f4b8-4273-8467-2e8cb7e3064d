"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/record/page",{

/***/ "(app-pages-browser)/./src/components/AudioRecorder.tsx":
/*!******************************************!*\
  !*** ./src/components/AudioRecorder.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AudioRecorder)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_Download_Mic_Pause_Play_Square_Trash2_Upload_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Mic,Pause,Play,Square,Trash2,Upload,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mic.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Mic_Pause_Play_Square_Trash2_Upload_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Mic,Pause,Play,Square,Trash2,Upload,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Mic_Pause_Play_Square_Trash2_Upload_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Mic,Pause,Play,Square,Trash2,Upload,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Mic_Pause_Play_Square_Trash2_Upload_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Mic,Pause,Play,Square,Trash2,Upload,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pause.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Mic_Pause_Play_Square_Trash2_Upload_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Mic,Pause,Play,Square,Trash2,Upload,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Mic_Pause_Play_Square_Trash2_Upload_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Mic,Pause,Play,Square,Trash2,Upload,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/volume-2.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Mic_Pause_Play_Square_Trash2_Upload_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Mic,Pause,Play,Square,Trash2,Upload,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Mic_Pause_Play_Square_Trash2_Upload_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Mic,Pause,Play,Square,Trash2,Upload,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _lib_audio__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/audio */ \"(app-pages-browser)/./src/lib/audio.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction AudioRecorder(param) {\n    let { onRecordingComplete, onRecordingStart, onRecordingStop, maxDuration = 120, className = '' } = param;\n    _s();\n    const [isRecording, setIsRecording] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isPlaying, setIsPlaying] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [audioBlob, setAudioBlob] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [audioUrl, setAudioUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [duration, setDuration] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [audioLevel, setAudioLevel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isInitialized, setIsInitialized] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [permissionStatus, setPermissionStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('unknown');\n    const recorderRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const audioRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const intervalRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const animationRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AudioRecorder.useEffect\": ()=>{\n            return ({\n                \"AudioRecorder.useEffect\": ()=>{\n                    cleanup();\n                }\n            })[\"AudioRecorder.useEffect\"];\n        }\n    }[\"AudioRecorder.useEffect\"], []);\n    const cleanup = ()=>{\n        if (intervalRef.current) {\n            clearInterval(intervalRef.current);\n        }\n        if (animationRef.current) {\n            cancelAnimationFrame(animationRef.current);\n        }\n        if (recorderRef.current) {\n            recorderRef.current.cleanup();\n        }\n        if (audioUrl) {\n            URL.revokeObjectURL(audioUrl);\n        }\n    };\n    const initializeRecorder = async ()=>{\n        try {\n            setError(null);\n            // Check if browser supports required APIs\n            if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {\n                throw new Error('Your browser does not support audio recording');\n            }\n            // Request microphone permission explicitly\n            console.log('Requesting microphone permission...');\n            const stream = await navigator.mediaDevices.getUserMedia({\n                audio: {\n                    sampleRate: 44100,\n                    channelCount: 1,\n                    echoCancellation: true,\n                    noiseSuppression: true,\n                    autoGainControl: true\n                }\n            });\n            // Stop the test stream\n            stream.getTracks().forEach((track)=>track.stop());\n            // Now initialize the recorder\n            recorderRef.current = new _lib_audio__WEBPACK_IMPORTED_MODULE_2__.AudioRecorder();\n            await recorderRef.current.initialize();\n            setIsInitialized(true);\n            console.log('Audio recorder initialized successfully');\n        } catch (err) {\n            console.error('Recorder initialization failed:', err);\n            if (err instanceof Error) {\n                if (err.name === 'NotAllowedError') {\n                    setError('Microphone access denied. Please allow microphone permissions and refresh the page.');\n                } else if (err.name === 'NotFoundError') {\n                    setError('No microphone found. Please connect a microphone and try again.');\n                } else if (err.name === 'NotSupportedError') {\n                    setError('Your browser does not support audio recording.');\n                } else {\n                    setError(\"Microphone error: \".concat(err.message));\n                }\n            } else {\n                setError('Failed to access microphone. Please check permissions.');\n            }\n        }\n    };\n    const startRecording = async ()=>{\n        try {\n            setError(null);\n            if (!recorderRef.current || !isInitialized) {\n                console.log('Initializing recorder...');\n                await initializeRecorder();\n            }\n            if (!recorderRef.current || !isInitialized) {\n                setError('Failed to initialize microphone. Please check permissions and try again.');\n                return;\n            }\n            console.log('Starting recording...');\n            setDuration(0);\n            recorderRef.current.startRecording();\n            setIsRecording(true);\n            onRecordingStart === null || onRecordingStart === void 0 ? void 0 : onRecordingStart();\n            // Start duration timer\n            intervalRef.current = setInterval(()=>{\n                setDuration((prev)=>{\n                    const newDuration = prev + 1;\n                    if (newDuration >= maxDuration) {\n                        stopRecording();\n                    }\n                    return newDuration;\n                });\n            }, 1000);\n            // Start audio level monitoring\n            const updateAudioLevel = ()=>{\n                if (recorderRef.current && isRecording) {\n                    setAudioLevel(recorderRef.current.getAudioLevel());\n                    animationRef.current = requestAnimationFrame(updateAudioLevel);\n                }\n            };\n            updateAudioLevel();\n        } catch (err) {\n            console.error('Recording start failed:', err);\n            setError('Failed to start recording. Please check microphone permissions.');\n        }\n    };\n    const stopRecording = async ()=>{\n        if (!recorderRef.current || !isRecording) return;\n        try {\n            const blob = await recorderRef.current.stopRecording();\n            setIsRecording(false);\n            setAudioLevel(0);\n            onRecordingStop === null || onRecordingStop === void 0 ? void 0 : onRecordingStop();\n            if (intervalRef.current) {\n                clearInterval(intervalRef.current);\n            }\n            if (animationRef.current) {\n                cancelAnimationFrame(animationRef.current);\n            }\n            // Clean up previous audio URL\n            if (audioUrl) {\n                URL.revokeObjectURL(audioUrl);\n            }\n            const newAudioUrl = _lib_audio__WEBPACK_IMPORTED_MODULE_2__.audioUtils.createAudioURL(blob);\n            setAudioBlob(blob);\n            setAudioUrl(newAudioUrl);\n            onRecordingComplete === null || onRecordingComplete === void 0 ? void 0 : onRecordingComplete(blob);\n        } catch (err) {\n            setError('Failed to stop recording');\n            console.error('Recording stop failed:', err);\n        }\n    };\n    const playAudio = ()=>{\n        if (!audioRef.current || !audioUrl) return;\n        if (isPlaying) {\n            audioRef.current.pause();\n            setIsPlaying(false);\n        } else {\n            audioRef.current.play();\n            setIsPlaying(true);\n        }\n    };\n    const downloadAudio = ()=>{\n        if (!audioBlob) return;\n        _lib_audio__WEBPACK_IMPORTED_MODULE_2__.audioUtils.downloadAudio(audioBlob, \"voice-recording-\".concat(Date.now(), \".webm\"));\n    };\n    const deleteRecording = ()=>{\n        if (audioUrl) {\n            URL.revokeObjectURL(audioUrl);\n        }\n        setAudioBlob(null);\n        setAudioUrl(null);\n        setDuration(0);\n        setIsPlaying(false);\n    };\n    const handleFileUpload = (event)=>{\n        var _event_target_files;\n        const file = (_event_target_files = event.target.files) === null || _event_target_files === void 0 ? void 0 : _event_target_files[0];\n        if (!file) return;\n        const validation = _lib_audio__WEBPACK_IMPORTED_MODULE_2__.audioUtils.validateAudioFile(file);\n        if (!validation.valid) {\n            setError(validation.error || 'Invalid audio file');\n            return;\n        }\n        setError(null);\n        const blob = new Blob([\n            file\n        ], {\n            type: file.type\n        });\n        const url = _lib_audio__WEBPACK_IMPORTED_MODULE_2__.audioUtils.createAudioURL(blob);\n        if (audioUrl) {\n            URL.revokeObjectURL(audioUrl);\n        }\n        setAudioBlob(blob);\n        setAudioUrl(url);\n        onRecordingComplete === null || onRecordingComplete === void 0 ? void 0 : onRecordingComplete(blob);\n    };\n    const formatTime = (seconds)=>{\n        const mins = Math.floor(seconds / 60);\n        const secs = seconds % 60;\n        return \"\".concat(mins, \":\").concat(secs.toString().padStart(2, '0'));\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6 \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.AnimatePresence, {\n                children: error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: -10\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    exit: {\n                        opacity: 0,\n                        y: -10\n                    },\n                    className: \"bg-red-900/50 border border-red-700 rounded-lg p-4 text-red-200\",\n                    children: error\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                    lineNumber: 243,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                lineNumber: 241,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"card\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold mb-2\",\n                                children: \"Record Your Voice\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                lineNumber: 257,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400\",\n                                children: [\n                                    \"Record at least 30 seconds for best results (max \",\n                                    maxDuration,\n                                    \"s)\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                lineNumber: 258,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                        lineNumber: 256,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-center mb-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"audio-visualizer\",\n                            children: [\n                                ...Array(12)\n                            ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"audio-bar w-3\",\n                                    style: {\n                                        height: isRecording ? \"\".concat(Math.max(4, audioLevel * 60 + Math.random() * 20), \"px\") : '4px'\n                                    }\n                                }, i, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                    lineNumber: 267,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                            lineNumber: 265,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                        lineNumber: 264,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-2xl font-mono text-purple-400\",\n                                children: formatTime(duration)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                lineNumber: 282,\n                                columnNumber: 11\n                            }, this),\n                            maxDuration && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-gray-400\",\n                                children: [\n                                    \"/ \",\n                                    formatTime(maxDuration)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                lineNumber: 286,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                        lineNumber: 281,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-center space-x-4 mb-6\",\n                        children: [\n                            !isRecording ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: startRecording,\n                                className: \"btn-primary flex items-center space-x-2\",\n                                disabled: isRecording,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Mic_Pause_Play_Square_Trash2_Upload_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                        lineNumber: 300,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Start Recording\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                        lineNumber: 301,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                lineNumber: 295,\n                                columnNumber: 13\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: stopRecording,\n                                className: \"bg-red-600 hover:bg-red-700 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-200 flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Mic_Pause_Play_Square_Trash2_Upload_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                        lineNumber: 308,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Stop Recording\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                        lineNumber: 309,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                lineNumber: 304,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"btn-secondary cursor-pointer flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Mic_Pause_Play_Square_Trash2_Upload_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                        lineNumber: 315,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Upload Audio\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                        lineNumber: 316,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"file\",\n                                        accept: \"audio/*\",\n                                        onChange: handleFileUpload,\n                                        className: \"hidden\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                        lineNumber: 317,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                lineNumber: 314,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                        lineNumber: 293,\n                        columnNumber: 9\n                    }, this),\n                    isRecording && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full bg-gray-700 rounded-full h-2 mb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gradient-to-r from-purple-600 to-blue-600 h-2 rounded-full transition-all duration-1000\",\n                            style: {\n                                width: \"\".concat(duration / maxDuration * 100, \"%\")\n                            }\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                            lineNumber: 329,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                        lineNumber: 328,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                lineNumber: 255,\n                columnNumber: 7\n            }, this),\n            audioUrl && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                initial: {\n                    opacity: 0,\n                    y: 20\n                },\n                animate: {\n                    opacity: 1,\n                    y: 0\n                },\n                className: \"card\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: playAudio,\n                                        className: \"btn-secondary flex items-center space-x-2\",\n                                        children: [\n                                            isPlaying ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Mic_Pause_Play_Square_Trash2_Upload_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                                lineNumber: 351,\n                                                columnNumber: 19\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Mic_Pause_Play_Square_Trash2_Upload_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                                lineNumber: 353,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: isPlaying ? 'Pause' : 'Play'\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                                lineNumber: 355,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                        lineNumber: 346,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2 text-gray-400\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Mic_Pause_Play_Square_Trash2_Upload_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                                lineNumber: 359,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Recording ready\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                                lineNumber: 360,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                        lineNumber: 358,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                lineNumber: 345,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: downloadAudio,\n                                        className: \"btn-secondary flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Mic_Pause_Play_Square_Trash2_Upload_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                                lineNumber: 369,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Download\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                                lineNumber: 370,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                        lineNumber: 365,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: deleteRecording,\n                                        className: \"text-red-400 hover:text-red-300 p-2 rounded-lg hover:bg-red-900/20 transition-colors\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Mic_Pause_Play_Square_Trash2_Upload_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                            lineNumber: 377,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                        lineNumber: 373,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                lineNumber: 364,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                        lineNumber: 344,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"audio\", {\n                        ref: audioRef,\n                        src: audioUrl,\n                        onEnded: ()=>setIsPlaying(false),\n                        onPlay: ()=>setIsPlaying(true),\n                        onPause: ()=>setIsPlaying(false),\n                        className: \"hidden\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                        lineNumber: 382,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                lineNumber: 339,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n        lineNumber: 239,\n        columnNumber: 5\n    }, this);\n}\n_s(AudioRecorder, \"bxOoKlINEtZOTkZWVpPpTXqxbyI=\");\n_c = AudioRecorder;\nvar _c;\n$RefreshReg$(_c, \"AudioRecorder\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/AudioRecorder.tsx\n"));

/***/ })

});