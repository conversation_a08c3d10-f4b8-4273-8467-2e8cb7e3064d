"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/record/page",{

/***/ "(app-pages-browser)/./src/components/AudioRecorder.tsx":
/*!******************************************!*\
  !*** ./src/components/AudioRecorder.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AudioRecorder)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_Download_Mic_Pause_Play_Square_Trash2_Upload_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Mic,Pause,Play,Square,Trash2,Upload,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mic.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Mic_Pause_Play_Square_Trash2_Upload_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Mic,Pause,Play,Square,Trash2,Upload,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Mic_Pause_Play_Square_Trash2_Upload_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Mic,Pause,Play,Square,Trash2,Upload,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Mic_Pause_Play_Square_Trash2_Upload_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Mic,Pause,Play,Square,Trash2,Upload,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pause.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Mic_Pause_Play_Square_Trash2_Upload_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Mic,Pause,Play,Square,Trash2,Upload,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Mic_Pause_Play_Square_Trash2_Upload_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Mic,Pause,Play,Square,Trash2,Upload,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/volume-2.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Mic_Pause_Play_Square_Trash2_Upload_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Mic,Pause,Play,Square,Trash2,Upload,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Mic_Pause_Play_Square_Trash2_Upload_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Mic,Pause,Play,Square,Trash2,Upload,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _lib_audio__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/audio */ \"(app-pages-browser)/./src/lib/audio.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction AudioRecorder(param) {\n    let { onRecordingComplete, onRecordingStart, onRecordingStop, maxDuration = 120, className = '' } = param;\n    _s();\n    const [isRecording, setIsRecording] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isPlaying, setIsPlaying] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [audioBlob, setAudioBlob] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [audioUrl, setAudioUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [duration, setDuration] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [audioLevel, setAudioLevel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isInitialized, setIsInitialized] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [permissionStatus, setPermissionStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('unknown');\n    const recorderRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const audioRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const intervalRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const animationRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AudioRecorder.useEffect\": ()=>{\n            // Check initial permission status and auto-initialize if granted\n            const initializeIfPermitted = {\n                \"AudioRecorder.useEffect.initializeIfPermitted\": async ()=>{\n                    await checkMicrophonePermission();\n                    // If permission is already granted, auto-initialize\n                    if (permissionStatus === 'granted') {\n                        await initializeRecorder();\n                    }\n                }\n            }[\"AudioRecorder.useEffect.initializeIfPermitted\"];\n            initializeIfPermitted();\n            return ({\n                \"AudioRecorder.useEffect\": ()=>{\n                    cleanup();\n                }\n            })[\"AudioRecorder.useEffect\"];\n        }\n    }[\"AudioRecorder.useEffect\"], []);\n    // Auto-initialize when permission status changes to granted\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AudioRecorder.useEffect\": ()=>{\n            if (permissionStatus === 'granted' && !isInitialized) {\n                initializeRecorder();\n            }\n        }\n    }[\"AudioRecorder.useEffect\"], [\n        permissionStatus,\n        isInitialized\n    ]);\n    const checkMicrophonePermission = async ()=>{\n        try {\n            if (navigator.permissions) {\n                const permission = await navigator.permissions.query({\n                    name: 'microphone'\n                });\n                setPermissionStatus(permission.state);\n                permission.onchange = ()=>{\n                    setPermissionStatus(permission.state);\n                };\n            } else {\n                // Fallback: try to access microphone to check permission\n                try {\n                    const stream = await navigator.mediaDevices.getUserMedia({\n                        audio: true\n                    });\n                    stream.getTracks().forEach((track)=>track.stop());\n                    setPermissionStatus('granted');\n                } catch (err) {\n                    setPermissionStatus('denied');\n                }\n            }\n        } catch (err) {\n            console.log('Permission check failed:', err);\n            setPermissionStatus('unknown');\n        }\n    };\n    const requestMicrophonePermission = async ()=>{\n        try {\n            setError(null);\n            console.log('Requesting microphone permission...');\n            const stream = await navigator.mediaDevices.getUserMedia({\n                audio: {\n                    sampleRate: 44100,\n                    channelCount: 1,\n                    echoCancellation: true,\n                    noiseSuppression: true,\n                    autoGainControl: true\n                }\n            });\n            // Stop the test stream immediately\n            stream.getTracks().forEach((track)=>track.stop());\n            setPermissionStatus('granted');\n            setError(null);\n            // Auto-initialize after permission granted\n            await initializeRecorder();\n        } catch (err) {\n            console.error('Permission request failed:', err);\n            setPermissionStatus('denied');\n            if (err instanceof Error) {\n                if (err.name === 'NotAllowedError') {\n                    setError('Microphone access denied. Please click the microphone icon in your browser\\'s address bar and allow access.');\n                } else if (err.name === 'NotFoundError') {\n                    setError('No microphone found. Please connect a microphone and try again.');\n                } else {\n                    setError(\"Microphone error: \".concat(err.message));\n                }\n            }\n        }\n    };\n    const cleanup = ()=>{\n        if (intervalRef.current) {\n            clearInterval(intervalRef.current);\n        }\n        if (animationRef.current) {\n            cancelAnimationFrame(animationRef.current);\n        }\n        if (recorderRef.current) {\n            recorderRef.current.cleanup();\n        }\n        if (audioUrl) {\n            URL.revokeObjectURL(audioUrl);\n        }\n    };\n    const initializeRecorder = async ()=>{\n        try {\n            setError(null);\n            // Check if browser supports required APIs\n            if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {\n                throw new Error('Your browser does not support audio recording');\n            }\n            // Request microphone permission explicitly\n            console.log('Requesting microphone permission...');\n            const stream = await navigator.mediaDevices.getUserMedia({\n                audio: {\n                    sampleRate: 44100,\n                    channelCount: 1,\n                    echoCancellation: true,\n                    noiseSuppression: true,\n                    autoGainControl: true\n                }\n            });\n            // Stop the test stream\n            stream.getTracks().forEach((track)=>track.stop());\n            // Now initialize the recorder\n            recorderRef.current = new _lib_audio__WEBPACK_IMPORTED_MODULE_2__.AudioRecorder();\n            await recorderRef.current.initialize();\n            setIsInitialized(true);\n            console.log('Audio recorder initialized successfully');\n        } catch (err) {\n            console.error('Recorder initialization failed:', err);\n            if (err instanceof Error) {\n                if (err.name === 'NotAllowedError') {\n                    setError('Microphone access denied. Please allow microphone permissions and refresh the page.');\n                } else if (err.name === 'NotFoundError') {\n                    setError('No microphone found. Please connect a microphone and try again.');\n                } else if (err.name === 'NotSupportedError') {\n                    setError('Your browser does not support audio recording.');\n                } else {\n                    setError(\"Microphone error: \".concat(err.message));\n                }\n            } else {\n                setError('Failed to access microphone. Please check permissions.');\n            }\n        }\n    };\n    const startRecording = async ()=>{\n        try {\n            setError(null);\n            if (!recorderRef.current || !isInitialized) {\n                console.log('Initializing recorder...');\n                await initializeRecorder();\n            }\n            if (!recorderRef.current || !isInitialized) {\n                setError('Failed to initialize microphone. Please check permissions and try again.');\n                return;\n            }\n            console.log('Starting recording...');\n            setDuration(0);\n            recorderRef.current.startRecording();\n            setIsRecording(true);\n            onRecordingStart === null || onRecordingStart === void 0 ? void 0 : onRecordingStart();\n            // Start duration timer\n            intervalRef.current = setInterval(()=>{\n                setDuration((prev)=>{\n                    const newDuration = prev + 1;\n                    if (newDuration >= maxDuration) {\n                        stopRecording();\n                    }\n                    return newDuration;\n                });\n            }, 1000);\n            // Start audio level monitoring\n            const updateAudioLevel = ()=>{\n                if (recorderRef.current && isRecording) {\n                    setAudioLevel(recorderRef.current.getAudioLevel());\n                    animationRef.current = requestAnimationFrame(updateAudioLevel);\n                }\n            };\n            updateAudioLevel();\n        } catch (err) {\n            console.error('Recording start failed:', err);\n            setError('Failed to start recording. Please check microphone permissions.');\n        }\n    };\n    const stopRecording = async ()=>{\n        if (!recorderRef.current || !isRecording) return;\n        try {\n            const blob = await recorderRef.current.stopRecording();\n            setIsRecording(false);\n            setAudioLevel(0);\n            onRecordingStop === null || onRecordingStop === void 0 ? void 0 : onRecordingStop();\n            if (intervalRef.current) {\n                clearInterval(intervalRef.current);\n            }\n            if (animationRef.current) {\n                cancelAnimationFrame(animationRef.current);\n            }\n            // Clean up previous audio URL\n            if (audioUrl) {\n                URL.revokeObjectURL(audioUrl);\n            }\n            const newAudioUrl = _lib_audio__WEBPACK_IMPORTED_MODULE_2__.audioUtils.createAudioURL(blob);\n            setAudioBlob(blob);\n            setAudioUrl(newAudioUrl);\n            onRecordingComplete === null || onRecordingComplete === void 0 ? void 0 : onRecordingComplete(blob);\n        } catch (err) {\n            setError('Failed to stop recording');\n            console.error('Recording stop failed:', err);\n        }\n    };\n    const playAudio = ()=>{\n        if (!audioRef.current || !audioUrl) return;\n        if (isPlaying) {\n            audioRef.current.pause();\n            setIsPlaying(false);\n        } else {\n            audioRef.current.play();\n            setIsPlaying(true);\n        }\n    };\n    const downloadAudio = ()=>{\n        if (!audioBlob) return;\n        _lib_audio__WEBPACK_IMPORTED_MODULE_2__.audioUtils.downloadAudio(audioBlob, \"voice-recording-\".concat(Date.now(), \".webm\"));\n    };\n    const deleteRecording = ()=>{\n        if (audioUrl) {\n            URL.revokeObjectURL(audioUrl);\n        }\n        setAudioBlob(null);\n        setAudioUrl(null);\n        setDuration(0);\n        setIsPlaying(false);\n    };\n    const handleFileUpload = (event)=>{\n        var _event_target_files;\n        const file = (_event_target_files = event.target.files) === null || _event_target_files === void 0 ? void 0 : _event_target_files[0];\n        if (!file) return;\n        const validation = _lib_audio__WEBPACK_IMPORTED_MODULE_2__.audioUtils.validateAudioFile(file);\n        if (!validation.valid) {\n            setError(validation.error || 'Invalid audio file');\n            return;\n        }\n        setError(null);\n        const blob = new Blob([\n            file\n        ], {\n            type: file.type\n        });\n        const url = _lib_audio__WEBPACK_IMPORTED_MODULE_2__.audioUtils.createAudioURL(blob);\n        if (audioUrl) {\n            URL.revokeObjectURL(audioUrl);\n        }\n        setAudioBlob(blob);\n        setAudioUrl(url);\n        onRecordingComplete === null || onRecordingComplete === void 0 ? void 0 : onRecordingComplete(blob);\n    };\n    const formatTime = (seconds)=>{\n        const mins = Math.floor(seconds / 60);\n        const secs = seconds % 60;\n        return \"\".concat(mins, \":\").concat(secs.toString().padStart(2, '0'));\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6 \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.AnimatePresence, {\n                children: error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: -10\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    exit: {\n                        opacity: 0,\n                        y: -10\n                    },\n                    className: \"bg-red-900/50 border border-red-700 rounded-lg p-4 text-red-200\",\n                    children: error\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                    lineNumber: 327,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                lineNumber: 325,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"card\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold mb-2\",\n                                children: \"Record Your Voice\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                lineNumber: 341,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400 mb-2\",\n                                children: [\n                                    \"Record at least 30 seconds for best results (max \",\n                                    maxDuration,\n                                    \"s)\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                lineNumber: 342,\n                                columnNumber: 11\n                            }, this),\n                            permissionStatus === 'unknown' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-blue-300\",\n                                children: \"\\uD83D\\uDCA1 On macOS: Make sure to allow microphone access in System Preferences → Security & Privacy → Microphone\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                lineNumber: 346,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                        lineNumber: 340,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-center mb-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"audio-visualizer\",\n                            children: [\n                                ...Array(12)\n                            ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"audio-bar w-3\",\n                                    style: {\n                                        height: isRecording ? \"\".concat(Math.max(4, audioLevel * 60 + Math.random() * 20), \"px\") : '4px'\n                                    }\n                                }, i, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                    lineNumber: 356,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                            lineNumber: 354,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                        lineNumber: 353,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-2xl font-mono text-purple-400\",\n                                children: formatTime(duration)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                lineNumber: 371,\n                                columnNumber: 11\n                            }, this),\n                            maxDuration && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-gray-400\",\n                                children: [\n                                    \"/ \",\n                                    formatTime(maxDuration)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                lineNumber: 375,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                        lineNumber: 370,\n                        columnNumber: 9\n                    }, this),\n                    permissionStatus === 'denied' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6 p-4 bg-yellow-900/50 border border-yellow-700 rounded-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2 text-yellow-200 mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Mic_Pause_Play_Square_Trash2_Upload_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                        lineNumber: 385,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: \"Microphone Permission Required\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                        lineNumber: 386,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                lineNumber: 384,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-yellow-300 mb-3\",\n                                children: \"Please allow microphone access to record your voice. Click the button below to request permission.\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                lineNumber: 388,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: requestMicrophonePermission,\n                                className: \"btn-primary flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Mic_Pause_Play_Square_Trash2_Upload_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                        lineNumber: 395,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Allow Microphone Access\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                        lineNumber: 396,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                lineNumber: 391,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                        lineNumber: 383,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-center space-x-4 mb-6\",\n                        children: [\n                            permissionStatus === 'unknown' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: requestMicrophonePermission,\n                                className: \"btn-primary flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Mic_Pause_Play_Square_Trash2_Upload_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                        lineNumber: 408,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Test Microphone\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                        lineNumber: 409,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                lineNumber: 404,\n                                columnNumber: 13\n                            }, this),\n                            permissionStatus === 'granted' && !isRecording && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: startRecording,\n                                className: \"btn-primary flex items-center space-x-2\",\n                                disabled: isRecording,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Mic_Pause_Play_Square_Trash2_Upload_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                        lineNumber: 419,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Start Recording\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                        lineNumber: 420,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                lineNumber: 414,\n                                columnNumber: 13\n                            }, this),\n                            isRecording && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: stopRecording,\n                                className: \"bg-red-600 hover:bg-red-700 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-200 flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Mic_Pause_Play_Square_Trash2_Upload_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                        lineNumber: 429,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Stop Recording\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                        lineNumber: 430,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                lineNumber: 425,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"btn-secondary cursor-pointer flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Mic_Pause_Play_Square_Trash2_Upload_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                        lineNumber: 436,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Upload Audio\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                        lineNumber: 437,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"file\",\n                                        accept: \"audio/*\",\n                                        onChange: handleFileUpload,\n                                        className: \"hidden\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                        lineNumber: 438,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                lineNumber: 435,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                        lineNumber: 402,\n                        columnNumber: 9\n                    }, this),\n                    isRecording && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full bg-gray-700 rounded-full h-2 mb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gradient-to-r from-purple-600 to-blue-600 h-2 rounded-full transition-all duration-1000\",\n                            style: {\n                                width: \"\".concat(duration / maxDuration * 100, \"%\")\n                            }\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                            lineNumber: 450,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                        lineNumber: 449,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                lineNumber: 339,\n                columnNumber: 7\n            }, this),\n            audioUrl && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                initial: {\n                    opacity: 0,\n                    y: 20\n                },\n                animate: {\n                    opacity: 1,\n                    y: 0\n                },\n                className: \"card\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: playAudio,\n                                        className: \"btn-secondary flex items-center space-x-2\",\n                                        children: [\n                                            isPlaying ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Mic_Pause_Play_Square_Trash2_Upload_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                                lineNumber: 472,\n                                                columnNumber: 19\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Mic_Pause_Play_Square_Trash2_Upload_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                                lineNumber: 474,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: isPlaying ? 'Pause' : 'Play'\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                                lineNumber: 476,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                        lineNumber: 467,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2 text-gray-400\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Mic_Pause_Play_Square_Trash2_Upload_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                                lineNumber: 480,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Recording ready\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                                lineNumber: 481,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                        lineNumber: 479,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                lineNumber: 466,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: downloadAudio,\n                                        className: \"btn-secondary flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Mic_Pause_Play_Square_Trash2_Upload_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                                lineNumber: 490,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Download\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                                lineNumber: 491,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                        lineNumber: 486,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: deleteRecording,\n                                        className: \"text-red-400 hover:text-red-300 p-2 rounded-lg hover:bg-red-900/20 transition-colors\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Mic_Pause_Play_Square_Trash2_Upload_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                            lineNumber: 498,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                        lineNumber: 494,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                lineNumber: 485,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                        lineNumber: 465,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"audio\", {\n                        ref: audioRef,\n                        src: audioUrl,\n                        onEnded: ()=>setIsPlaying(false),\n                        onPlay: ()=>setIsPlaying(true),\n                        onPause: ()=>setIsPlaying(false),\n                        className: \"hidden\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                        lineNumber: 503,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                lineNumber: 460,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n        lineNumber: 323,\n        columnNumber: 5\n    }, this);\n}\n_s(AudioRecorder, \"Mwz7UGCEwx3xfHZdpCMFx7VY5WU=\");\n_c = AudioRecorder;\nvar _c;\n$RefreshReg$(_c, \"AudioRecorder\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/AudioRecorder.tsx\n"));

/***/ })

});