"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/record/page",{

/***/ "(app-pages-browser)/./src/components/AudioRecorder.tsx":
/*!******************************************!*\
  !*** ./src/components/AudioRecorder.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AudioRecorder)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_Download_Mic_Pause_Play_Square_Trash2_Upload_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Mic,Pause,Play,Square,Trash2,Upload,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mic.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Mic_Pause_Play_Square_Trash2_Upload_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Mic,Pause,Play,Square,Trash2,Upload,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Mic_Pause_Play_Square_Trash2_Upload_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Mic,Pause,Play,Square,Trash2,Upload,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Mic_Pause_Play_Square_Trash2_Upload_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Mic,Pause,Play,Square,Trash2,Upload,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pause.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Mic_Pause_Play_Square_Trash2_Upload_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Mic,Pause,Play,Square,Trash2,Upload,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Mic_Pause_Play_Square_Trash2_Upload_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Mic,Pause,Play,Square,Trash2,Upload,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/volume-2.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Mic_Pause_Play_Square_Trash2_Upload_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Mic,Pause,Play,Square,Trash2,Upload,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Mic_Pause_Play_Square_Trash2_Upload_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Mic,Pause,Play,Square,Trash2,Upload,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _lib_audio__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/audio */ \"(app-pages-browser)/./src/lib/audio.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction AudioRecorder(param) {\n    let { onRecordingComplete, onRecordingStart, onRecordingStop, maxDuration = 120, className = '' } = param;\n    _s();\n    const [isRecording, setIsRecording] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isPlaying, setIsPlaying] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [audioBlob, setAudioBlob] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [audioUrl, setAudioUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [duration, setDuration] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [audioLevel, setAudioLevel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isInitialized, setIsInitialized] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const recorderRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const audioRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const intervalRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const animationRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AudioRecorder.useEffect\": ()=>{\n            return ({\n                \"AudioRecorder.useEffect\": ()=>{\n                    cleanup();\n                }\n            })[\"AudioRecorder.useEffect\"];\n        }\n    }[\"AudioRecorder.useEffect\"], []);\n    const cleanup = ()=>{\n        if (intervalRef.current) {\n            clearInterval(intervalRef.current);\n        }\n        if (animationRef.current) {\n            cancelAnimationFrame(animationRef.current);\n        }\n        if (recorderRef.current) {\n            recorderRef.current.cleanup();\n        }\n        if (audioUrl) {\n            URL.revokeObjectURL(audioUrl);\n        }\n    };\n    const initializeRecorder = async ()=>{\n        try {\n            setError(null);\n            // Check if browser supports required APIs\n            if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {\n                throw new Error('Your browser does not support audio recording');\n            }\n            // Request microphone permission explicitly\n            console.log('Requesting microphone permission...');\n            const stream = await navigator.mediaDevices.getUserMedia({\n                audio: {\n                    sampleRate: 44100,\n                    channelCount: 1,\n                    echoCancellation: true,\n                    noiseSuppression: true,\n                    autoGainControl: true\n                }\n            });\n            // Stop the test stream\n            stream.getTracks().forEach((track)=>track.stop());\n            // Now initialize the recorder\n            recorderRef.current = new _lib_audio__WEBPACK_IMPORTED_MODULE_2__.AudioRecorder();\n            await recorderRef.current.initialize();\n            setIsInitialized(true);\n            console.log('Audio recorder initialized successfully');\n        } catch (err) {\n            console.error('Recorder initialization failed:', err);\n            if (err instanceof Error) {\n                if (err.name === 'NotAllowedError') {\n                    setError('Microphone access denied. Please allow microphone permissions and refresh the page.');\n                } else if (err.name === 'NotFoundError') {\n                    setError('No microphone found. Please connect a microphone and try again.');\n                } else if (err.name === 'NotSupportedError') {\n                    setError('Your browser does not support audio recording.');\n                } else {\n                    setError(\"Microphone error: \".concat(err.message));\n                }\n            } else {\n                setError('Failed to access microphone. Please check permissions.');\n            }\n        }\n    };\n    const startRecording = async ()=>{\n        if (!recorderRef.current) {\n            await initializeRecorder();\n        }\n        if (!recorderRef.current || !isInitialized) {\n            setError('Microphone not available');\n            return;\n        }\n        try {\n            setError(null);\n            setDuration(0);\n            recorderRef.current.startRecording();\n            setIsRecording(true);\n            onRecordingStart === null || onRecordingStart === void 0 ? void 0 : onRecordingStart();\n            // Start duration timer\n            intervalRef.current = setInterval(()=>{\n                setDuration((prev)=>{\n                    const newDuration = prev + 1;\n                    if (newDuration >= maxDuration) {\n                        stopRecording();\n                    }\n                    return newDuration;\n                });\n            }, 1000);\n            // Start audio level monitoring\n            const updateAudioLevel = ()=>{\n                if (recorderRef.current && isRecording) {\n                    setAudioLevel(recorderRef.current.getAudioLevel());\n                    animationRef.current = requestAnimationFrame(updateAudioLevel);\n                }\n            };\n            updateAudioLevel();\n        } catch (err) {\n            setError('Failed to start recording');\n            console.error('Recording start failed:', err);\n        }\n    };\n    const stopRecording = async ()=>{\n        if (!recorderRef.current || !isRecording) return;\n        try {\n            const blob = await recorderRef.current.stopRecording();\n            setIsRecording(false);\n            setAudioLevel(0);\n            onRecordingStop === null || onRecordingStop === void 0 ? void 0 : onRecordingStop();\n            if (intervalRef.current) {\n                clearInterval(intervalRef.current);\n            }\n            if (animationRef.current) {\n                cancelAnimationFrame(animationRef.current);\n            }\n            // Clean up previous audio URL\n            if (audioUrl) {\n                URL.revokeObjectURL(audioUrl);\n            }\n            const newAudioUrl = _lib_audio__WEBPACK_IMPORTED_MODULE_2__.audioUtils.createAudioURL(blob);\n            setAudioBlob(blob);\n            setAudioUrl(newAudioUrl);\n            onRecordingComplete === null || onRecordingComplete === void 0 ? void 0 : onRecordingComplete(blob);\n        } catch (err) {\n            setError('Failed to stop recording');\n            console.error('Recording stop failed:', err);\n        }\n    };\n    const playAudio = ()=>{\n        if (!audioRef.current || !audioUrl) return;\n        if (isPlaying) {\n            audioRef.current.pause();\n            setIsPlaying(false);\n        } else {\n            audioRef.current.play();\n            setIsPlaying(true);\n        }\n    };\n    const downloadAudio = ()=>{\n        if (!audioBlob) return;\n        _lib_audio__WEBPACK_IMPORTED_MODULE_2__.audioUtils.downloadAudio(audioBlob, \"voice-recording-\".concat(Date.now(), \".webm\"));\n    };\n    const deleteRecording = ()=>{\n        if (audioUrl) {\n            URL.revokeObjectURL(audioUrl);\n        }\n        setAudioBlob(null);\n        setAudioUrl(null);\n        setDuration(0);\n        setIsPlaying(false);\n    };\n    const handleFileUpload = (event)=>{\n        var _event_target_files;\n        const file = (_event_target_files = event.target.files) === null || _event_target_files === void 0 ? void 0 : _event_target_files[0];\n        if (!file) return;\n        const validation = _lib_audio__WEBPACK_IMPORTED_MODULE_2__.audioUtils.validateAudioFile(file);\n        if (!validation.valid) {\n            setError(validation.error || 'Invalid audio file');\n            return;\n        }\n        setError(null);\n        const blob = new Blob([\n            file\n        ], {\n            type: file.type\n        });\n        const url = _lib_audio__WEBPACK_IMPORTED_MODULE_2__.audioUtils.createAudioURL(blob);\n        if (audioUrl) {\n            URL.revokeObjectURL(audioUrl);\n        }\n        setAudioBlob(blob);\n        setAudioUrl(url);\n        onRecordingComplete === null || onRecordingComplete === void 0 ? void 0 : onRecordingComplete(blob);\n    };\n    const formatTime = (seconds)=>{\n        const mins = Math.floor(seconds / 60);\n        const secs = seconds % 60;\n        return \"\".concat(mins, \":\").concat(secs.toString().padStart(2, '0'));\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6 \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.AnimatePresence, {\n                children: error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: -10\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    exit: {\n                        opacity: 0,\n                        y: -10\n                    },\n                    className: \"bg-red-900/50 border border-red-700 rounded-lg p-4 text-red-200\",\n                    children: error\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                    lineNumber: 239,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                lineNumber: 237,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"card\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold mb-2\",\n                                children: \"Record Your Voice\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                lineNumber: 253,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400\",\n                                children: [\n                                    \"Record at least 30 seconds for best results (max \",\n                                    maxDuration,\n                                    \"s)\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                lineNumber: 254,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                        lineNumber: 252,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-center mb-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"audio-visualizer\",\n                            children: [\n                                ...Array(12)\n                            ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"audio-bar w-3\",\n                                    style: {\n                                        height: isRecording ? \"\".concat(Math.max(4, audioLevel * 60 + Math.random() * 20), \"px\") : '4px'\n                                    }\n                                }, i, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                    lineNumber: 263,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                            lineNumber: 261,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                        lineNumber: 260,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-2xl font-mono text-purple-400\",\n                                children: formatTime(duration)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                lineNumber: 278,\n                                columnNumber: 11\n                            }, this),\n                            maxDuration && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-gray-400\",\n                                children: [\n                                    \"/ \",\n                                    formatTime(maxDuration)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                lineNumber: 282,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                        lineNumber: 277,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-center space-x-4 mb-6\",\n                        children: [\n                            !isRecording ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: startRecording,\n                                className: \"btn-primary flex items-center space-x-2\",\n                                disabled: isRecording,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Mic_Pause_Play_Square_Trash2_Upload_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                        lineNumber: 296,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Start Recording\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                        lineNumber: 297,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                lineNumber: 291,\n                                columnNumber: 13\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: stopRecording,\n                                className: \"bg-red-600 hover:bg-red-700 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-200 flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Mic_Pause_Play_Square_Trash2_Upload_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                        lineNumber: 304,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Stop Recording\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                        lineNumber: 305,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                lineNumber: 300,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"btn-secondary cursor-pointer flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Mic_Pause_Play_Square_Trash2_Upload_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                        lineNumber: 311,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Upload Audio\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                        lineNumber: 312,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"file\",\n                                        accept: \"audio/*\",\n                                        onChange: handleFileUpload,\n                                        className: \"hidden\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                        lineNumber: 313,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                lineNumber: 310,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                        lineNumber: 289,\n                        columnNumber: 9\n                    }, this),\n                    isRecording && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full bg-gray-700 rounded-full h-2 mb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gradient-to-r from-purple-600 to-blue-600 h-2 rounded-full transition-all duration-1000\",\n                            style: {\n                                width: \"\".concat(duration / maxDuration * 100, \"%\")\n                            }\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                            lineNumber: 325,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                        lineNumber: 324,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                lineNumber: 251,\n                columnNumber: 7\n            }, this),\n            audioUrl && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                initial: {\n                    opacity: 0,\n                    y: 20\n                },\n                animate: {\n                    opacity: 1,\n                    y: 0\n                },\n                className: \"card\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: playAudio,\n                                        className: \"btn-secondary flex items-center space-x-2\",\n                                        children: [\n                                            isPlaying ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Mic_Pause_Play_Square_Trash2_Upload_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                                lineNumber: 347,\n                                                columnNumber: 19\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Mic_Pause_Play_Square_Trash2_Upload_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                                lineNumber: 349,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: isPlaying ? 'Pause' : 'Play'\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                                lineNumber: 351,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                        lineNumber: 342,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2 text-gray-400\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Mic_Pause_Play_Square_Trash2_Upload_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                                lineNumber: 355,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Recording ready\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                                lineNumber: 356,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                        lineNumber: 354,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                lineNumber: 341,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: downloadAudio,\n                                        className: \"btn-secondary flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Mic_Pause_Play_Square_Trash2_Upload_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                                lineNumber: 365,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Download\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                                lineNumber: 366,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                        lineNumber: 361,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: deleteRecording,\n                                        className: \"text-red-400 hover:text-red-300 p-2 rounded-lg hover:bg-red-900/20 transition-colors\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Mic_Pause_Play_Square_Trash2_Upload_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                            lineNumber: 373,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                        lineNumber: 369,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                lineNumber: 360,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                        lineNumber: 340,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"audio\", {\n                        ref: audioRef,\n                        src: audioUrl,\n                        onEnded: ()=>setIsPlaying(false),\n                        onPlay: ()=>setIsPlaying(true),\n                        onPause: ()=>setIsPlaying(false),\n                        className: \"hidden\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                        lineNumber: 378,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                lineNumber: 335,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n        lineNumber: 235,\n        columnNumber: 5\n    }, this);\n}\n_s(AudioRecorder, \"0MYx0H1S8d1mQNsKNDWK75M5ObM=\");\n_c = AudioRecorder;\nvar _c;\n$RefreshReg$(_c, \"AudioRecorder\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/AudioRecorder.tsx\n"));

/***/ })

});