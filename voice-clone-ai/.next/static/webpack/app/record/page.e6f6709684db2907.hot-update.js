"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/record/page",{

/***/ "(app-pages-browser)/./src/app/record/page.tsx":
/*!*********************************!*\
  !*** ./src/app/record/page.tsx ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RecordPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_AudioWaveform_Mic_Sparkles_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,AudioWaveform,Mic,Sparkles,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_AudioWaveform_Mic_Sparkles_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,AudioWaveform,Mic,Sparkles,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_AudioWaveform_Mic_Sparkles_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,AudioWaveform,Mic,Sparkles,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_AudioWaveform_Mic_Sparkles_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,AudioWaveform,Mic,Sparkles,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mic.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_AudioWaveform_Mic_Sparkles_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,AudioWaveform,Mic,Sparkles,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/audio-waveform.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_AudioWaveform_Mic_Sparkles_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,AudioWaveform,Mic,Sparkles,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/volume-2.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_AudioRecorder__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/AudioRecorder */ \"(app-pages-browser)/./src/components/AudioRecorder.tsx\");\n/* harmony import */ var _lib_localStorage__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/localStorage */ \"(app-pages-browser)/./src/lib/localStorage.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction RecordPage() {\n    _s();\n    const [step, setStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [voiceName, setVoiceName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [voiceDescription, setVoiceDescription] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [audioBlob, setAudioBlob] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isProcessing, setIsProcessing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleRecordingComplete = (blob)=>{\n        setAudioBlob(blob);\n    };\n    const handleNext = ()=>{\n        if (step === 1 && voiceName.trim()) {\n            setStep(2);\n        } else if (step === 2 && audioBlob) {\n            setStep(3);\n        }\n    };\n    const handleCreateVoiceClone = async ()=>{\n        if (!audioBlob || !voiceName.trim()) return;\n        setIsProcessing(true);\n        try {\n            console.log('Creating voice clone...', {\n                voiceName,\n                voiceDescription,\n                audioBlob\n            });\n            // Create FormData for the API call\n            const formData = new FormData();\n            formData.append('name', voiceName.trim());\n            formData.append('description', voiceDescription.trim() || \"Voice clone: \".concat(voiceName));\n            formData.append('audio', audioBlob, 'voice-sample.webm');\n            // Call the voice cloning API\n            const response = await fetch('/api/voice-clone', {\n                method: 'POST',\n                body: formData\n            });\n            const result = await response.json();\n            if (!response.ok) {\n                throw new Error(result.error || 'Failed to create voice clone');\n            }\n            console.log('Voice clone created successfully:', result);\n            // Save to localStorage as backup\n            if (result.voiceModel) {\n                _lib_localStorage__WEBPACK_IMPORTED_MODULE_4__.localStorageService.saveVoiceModel({\n                    id: result.voiceModel.id,\n                    name: result.voiceModel.name,\n                    description: result.voiceModel.description,\n                    voice_id: result.voiceModel.voice_id,\n                    created_at: result.voiceModel.created_at || new Date().toISOString()\n                });\n            }\n            // Redirect to generation page\n            window.location.href = '/generate';\n        } catch (error) {\n            console.error('Voice cloning failed:', error);\n            // Show error message to user\n            let errorMessage = 'Failed to create voice clone. ';\n            if (error instanceof Error) {\n                if (error.message.includes('quota')) {\n                    errorMessage += 'API quota exceeded. Please try again later.';\n                } else if (error.message.includes('unauthorized')) {\n                    errorMessage += 'API configuration error. Please check settings.';\n                } else {\n                    errorMessage += error.message;\n                }\n            } else {\n                errorMessage += 'Please try again.';\n            }\n            alert(errorMessage) // For now, we'll use alert. In production, you'd want a proper toast/notification\n            ;\n        } finally{\n            setIsProcessing(false);\n        }\n    };\n    const steps = [\n        {\n            number: 1,\n            title: 'Voice Details',\n            description: 'Name your voice clone'\n        },\n        {\n            number: 2,\n            title: 'Record Audio',\n            description: 'Capture your voice'\n        },\n        {\n            number: 3,\n            title: 'Create Clone',\n            description: 'Generate your AI voice'\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen py-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"fixed top-0 w-full z-50 bg-black/80 backdrop-blur-md border-b border-gray-800\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center h-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/\",\n                                className: \"flex items-center space-x-2 text-gray-300 hover:text-white transition-colors\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_AudioWaveform_Mic_Sparkles_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                        lineNumber: 105,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Back to Home\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                        lineNumber: 106,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                lineNumber: 104,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_AudioWaveform_Mic_Sparkles_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"w-8 h-8 text-purple-400\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                        lineNumber: 109,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xl font-bold gradient-text\",\n                                        children: \"VoiceClone AI\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                        lineNumber: 110,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                lineNumber: 108,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                        lineNumber: 103,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                    lineNumber: 102,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                lineNumber: 101,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"pt-24 px-4 sm:px-6 lg:px-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-4xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-12\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center space-x-8\",\n                                children: steps.map((stepItem, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-12 h-12 rounded-full flex items-center justify-center font-semibold transition-all duration-300 \".concat(step >= stepItem.number ? 'bg-gradient-to-r from-purple-600 to-blue-600 text-white' : 'bg-gray-800 text-gray-400'),\n                                                        children: stepItem.number\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                                        lineNumber: 124,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mt-2 text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm font-medium\",\n                                                                children: stepItem.title\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                                                lineNumber: 134,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-gray-400\",\n                                                                children: stepItem.description\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                                                lineNumber: 135,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                                        lineNumber: 133,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                                lineNumber: 123,\n                                                columnNumber: 19\n                                            }, this),\n                                            index < steps.length - 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-16 h-1 mx-4 transition-all duration-300 \".concat(step > stepItem.number ? 'bg-gradient-to-r from-purple-600 to-blue-600' : 'bg-gray-800')\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                                lineNumber: 139,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, stepItem.number, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                        lineNumber: 122,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                lineNumber: 120,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                            lineNumber: 119,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                x: 20\n                            },\n                            animate: {\n                                opacity: 1,\n                                x: 0\n                            },\n                            exit: {\n                                opacity: 0,\n                                x: -20\n                            },\n                            transition: {\n                                duration: 0.3\n                            },\n                            children: [\n                                step === 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card max-w-2xl mx-auto\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center mb-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"text-3xl font-bold mb-4\",\n                                                    children: [\n                                                        \"Create Your \",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"gradient-text\",\n                                                            children: \"Voice Clone\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                                            lineNumber: 164,\n                                                            columnNumber: 33\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                                    lineNumber: 163,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-300\",\n                                                    children: \"Let's start by giving your voice clone a name and description\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                                    lineNumber: 166,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                            lineNumber: 162,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium mb-2\",\n                                                            children: \"Voice Name *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                                            lineNumber: 173,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            value: voiceName,\n                                                            onChange: (e)=>setVoiceName(e.target.value),\n                                                            placeholder: \"e.g., My Professional Voice\",\n                                                            className: \"input-field w-full\",\n                                                            maxLength: 50\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                                            lineNumber: 176,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-gray-400 mt-1\",\n                                                            children: [\n                                                                voiceName.length,\n                                                                \"/50 characters\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                                            lineNumber: 184,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                                    lineNumber: 172,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium mb-2\",\n                                                            children: \"Description (Optional)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                                            lineNumber: 190,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                            value: voiceDescription,\n                                                            onChange: (e)=>setVoiceDescription(e.target.value),\n                                                            placeholder: \"Describe the tone, style, or intended use of this voice...\",\n                                                            className: \"input-field w-full h-24 resize-none\",\n                                                            maxLength: 200\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                                            lineNumber: 193,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-gray-400 mt-1\",\n                                                            children: [\n                                                                voiceDescription.length,\n                                                                \"/200 characters\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                                            lineNumber: 200,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                                    lineNumber: 189,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: handleNext,\n                                                    disabled: !voiceName.trim(),\n                                                    className: \"btn-primary w-full flex items-center justify-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Continue to Recording\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                                            lineNumber: 210,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_AudioWaveform_Mic_Sparkles_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                            className: \"w-5 h-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                                            lineNumber: 211,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                                    lineNumber: 205,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                            lineNumber: 171,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                    lineNumber: 161,\n                                    columnNumber: 15\n                                }, this),\n                                step === 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"max-w-3xl mx-auto\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center mb-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"text-3xl font-bold mb-4\",\n                                                    children: [\n                                                        \"Record Your \",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"gradient-text\",\n                                                            children: \"Voice Sample\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                                            lineNumber: 221,\n                                                            columnNumber: 33\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                                    lineNumber: 220,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-300 max-w-2xl mx-auto\",\n                                                    children: \"Record at least 30 seconds of clear speech. Read naturally and avoid background noise for the best results.\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                                    lineNumber: 223,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                            lineNumber: 219,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AudioRecorder__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            onRecordingComplete: handleRecordingComplete,\n                                            maxDuration: 120,\n                                            className: \"mb-8\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                            lineNumber: 228,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setStep(1),\n                                                    className: \"btn-secondary flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_AudioWaveform_Mic_Sparkles_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                            className: \"w-5 h-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                                            lineNumber: 239,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Back\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                                            lineNumber: 240,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                                    lineNumber: 235,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: handleNext,\n                                                    disabled: !audioBlob,\n                                                    className: \"btn-primary flex items-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Create Voice Clone\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                                            lineNumber: 248,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_AudioWaveform_Mic_Sparkles_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                            className: \"w-5 h-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                                            lineNumber: 249,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                                    lineNumber: 243,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                            lineNumber: 234,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                    lineNumber: 218,\n                                    columnNumber: 15\n                                }, this),\n                                step === 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card max-w-2xl mx-auto text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"text-3xl font-bold mb-4\",\n                                                    children: [\n                                                        \"Creating Your \",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"gradient-text\",\n                                                            children: \"Voice Clone\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                                            lineNumber: 259,\n                                                            columnNumber: 35\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                                    lineNumber: 258,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-300\",\n                                                    children: \"Our AI is analyzing your voice and creating your digital twin...\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                                    lineNumber: 261,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                            lineNumber: 257,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"audio-visualizer\",\n                                                        children: [\n                                                            ...Array(16)\n                                                        ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"waveform-bar w-2 bg-gradient-to-t from-purple-600 to-blue-400 rounded-full\",\n                                                                style: {\n                                                                    animationDelay: \"\".concat(i * 0.1, \"s\"),\n                                                                    height: \"\".concat(Math.random() * 40 + 10, \"px\")\n                                                                }\n                                                            }, i, false, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                                                lineNumber: 270,\n                                                                columnNumber: 25\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                                        lineNumber: 268,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                                    lineNumber: 267,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-center space-x-3 text-gray-300\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_AudioWaveform_Mic_Sparkles_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                    className: \"w-5 h-5 text-purple-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                                                    lineNumber: 284,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Analyzing voice patterns...\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                                                    lineNumber: 285,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                                            lineNumber: 283,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-center space-x-3 text-gray-300\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_AudioWaveform_Mic_Sparkles_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                    className: \"w-5 h-5 text-blue-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                                                    lineNumber: 288,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Training AI model...\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                                                    lineNumber: 289,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                                            lineNumber: 287,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-center space-x-3 text-gray-300\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_AudioWaveform_Mic_Sparkles_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                    className: \"w-5 h-5 text-green-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                                                    lineNumber: 292,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Optimizing voice synthesis...\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                                                    lineNumber: 293,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                                            lineNumber: 291,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                                    lineNumber: 282,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: handleCreateVoiceClone,\n                                                    disabled: isProcessing,\n                                                    className: \"btn-primary w-full disabled:opacity-50 disabled:cursor-not-allowed\",\n                                                    children: isProcessing ? 'Processing...' : 'Complete Setup'\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                                    lineNumber: 297,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                            lineNumber: 266,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                    lineNumber: 256,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, step, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                            lineNumber: 153,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                    lineNumber: 117,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                lineNumber: 116,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n        lineNumber: 99,\n        columnNumber: 5\n    }, this);\n}\n_s(RecordPage, \"cCyWoCumTgdAS+iOPSjr7zHZDSc=\");\n_c = RecordPage;\nvar _c;\n$RefreshReg$(_c, \"RecordPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/record/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/localStorage.ts":
/*!*********************************!*\
  !*** ./src/lib/localStorage.ts ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   localStorageService: () => (/* binding */ localStorageService)\n/* harmony export */ });\n// Local storage utilities for voice models\n// This serves as a fallback when Supabase is not available\nconst VOICE_MODELS_KEY = 'voice_clone_models';\nconst localStorageService = {\n    // Get all voice models from localStorage\n    getVoiceModels () {\n        if (false) {}\n        try {\n            const stored = localStorage.getItem(VOICE_MODELS_KEY);\n            return stored ? JSON.parse(stored) : [];\n        } catch (error) {\n            console.error('Error reading voice models from localStorage:', error);\n            return [];\n        }\n    },\n    // Save a voice model to localStorage\n    saveVoiceModel (voiceModel) {\n        if (false) {}\n        try {\n            const existing = this.getVoiceModels();\n            const updated = [\n                ...existing,\n                voiceModel\n            ];\n            localStorage.setItem(VOICE_MODELS_KEY, JSON.stringify(updated));\n        } catch (error) {\n            console.error('Error saving voice model to localStorage:', error);\n        }\n    },\n    // Remove a voice model from localStorage\n    removeVoiceModel (voiceId) {\n        if (false) {}\n        try {\n            const existing = this.getVoiceModels();\n            const filtered = existing.filter((model)=>model.voice_id !== voiceId);\n            localStorage.setItem(VOICE_MODELS_KEY, JSON.stringify(filtered));\n        } catch (error) {\n            console.error('Error removing voice model from localStorage:', error);\n        }\n    },\n    // Clear all voice models\n    clearVoiceModels () {\n        if (false) {}\n        try {\n            localStorage.removeItem(VOICE_MODELS_KEY);\n        } catch (error) {\n            console.error('Error clearing voice models from localStorage:', error);\n        }\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/localStorage.ts\n"));

/***/ })

});