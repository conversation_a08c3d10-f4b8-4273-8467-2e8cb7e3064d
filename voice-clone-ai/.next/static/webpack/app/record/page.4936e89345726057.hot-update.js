"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/record/page",{

/***/ "(app-pages-browser)/./src/components/AudioRecorder.tsx":
/*!******************************************!*\
  !*** ./src/components/AudioRecorder.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AudioRecorder)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_Download_Mic_Pause_Play_Square_Trash2_Upload_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Mic,Pause,Play,Square,Trash2,Upload,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mic.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Mic_Pause_Play_Square_Trash2_Upload_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Mic,Pause,Play,Square,Trash2,Upload,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Mic_Pause_Play_Square_Trash2_Upload_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Mic,Pause,Play,Square,Trash2,Upload,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Mic_Pause_Play_Square_Trash2_Upload_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Mic,Pause,Play,Square,Trash2,Upload,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pause.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Mic_Pause_Play_Square_Trash2_Upload_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Mic,Pause,Play,Square,Trash2,Upload,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Mic_Pause_Play_Square_Trash2_Upload_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Mic,Pause,Play,Square,Trash2,Upload,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/volume-2.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Mic_Pause_Play_Square_Trash2_Upload_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Mic,Pause,Play,Square,Trash2,Upload,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Mic_Pause_Play_Square_Trash2_Upload_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Mic,Pause,Play,Square,Trash2,Upload,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _lib_audio__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/audio */ \"(app-pages-browser)/./src/lib/audio.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction AudioRecorder(param) {\n    let { onRecordingComplete, onRecordingStart, onRecordingStop, maxDuration = 120, className = '' } = param;\n    _s();\n    const [isRecording, setIsRecording] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isPlaying, setIsPlaying] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [audioBlob, setAudioBlob] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [audioUrl, setAudioUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [duration, setDuration] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [audioLevel, setAudioLevel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isInitialized, setIsInitialized] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const recorderRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const audioRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const intervalRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const animationRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AudioRecorder.useEffect\": ()=>{\n            return ({\n                \"AudioRecorder.useEffect\": ()=>{\n                    cleanup();\n                }\n            })[\"AudioRecorder.useEffect\"];\n        }\n    }[\"AudioRecorder.useEffect\"], []);\n    const cleanup = ()=>{\n        if (intervalRef.current) {\n            clearInterval(intervalRef.current);\n        }\n        if (animationRef.current) {\n            cancelAnimationFrame(animationRef.current);\n        }\n        if (recorderRef.current) {\n            recorderRef.current.cleanup();\n        }\n        if (audioUrl) {\n            URL.revokeObjectURL(audioUrl);\n        }\n    };\n    const initializeRecorder = async ()=>{\n        try {\n            setError(null);\n            // Check if browser supports required APIs\n            if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {\n                throw new Error('Your browser does not support audio recording');\n            }\n            // Request microphone permission explicitly\n            console.log('Requesting microphone permission...');\n            const stream = await navigator.mediaDevices.getUserMedia({\n                audio: {\n                    sampleRate: 44100,\n                    channelCount: 1,\n                    echoCancellation: true,\n                    noiseSuppression: true,\n                    autoGainControl: true\n                }\n            });\n            // Stop the test stream\n            stream.getTracks().forEach((track)=>track.stop());\n            // Now initialize the recorder\n            recorderRef.current = new _lib_audio__WEBPACK_IMPORTED_MODULE_2__.AudioRecorder();\n            await recorderRef.current.initialize();\n            setIsInitialized(true);\n            console.log('Audio recorder initialized successfully');\n        } catch (err) {\n            console.error('Recorder initialization failed:', err);\n            if (err instanceof Error) {\n                if (err.name === 'NotAllowedError') {\n                    setError('Microphone access denied. Please allow microphone permissions and refresh the page.');\n                } else if (err.name === 'NotFoundError') {\n                    setError('No microphone found. Please connect a microphone and try again.');\n                } else if (err.name === 'NotSupportedError') {\n                    setError('Your browser does not support audio recording.');\n                } else {\n                    setError(\"Microphone error: \".concat(err.message));\n                }\n            } else {\n                setError('Failed to access microphone. Please check permissions.');\n            }\n        }\n    };\n    const startRecording = async ()=>{\n        try {\n            setError(null);\n            if (!recorderRef.current || !isInitialized) {\n                console.log('Initializing recorder...');\n                await initializeRecorder();\n            }\n            if (!recorderRef.current || !isInitialized) {\n                setError('Failed to initialize microphone. Please check permissions and try again.');\n                return;\n            }\n            console.log('Starting recording...');\n            setDuration(0);\n            recorderRef.current.startRecording();\n            setIsRecording(true);\n            onRecordingStart === null || onRecordingStart === void 0 ? void 0 : onRecordingStart();\n            // Start duration timer\n            intervalRef.current = setInterval(()=>{\n                setDuration((prev)=>{\n                    const newDuration = prev + 1;\n                    if (newDuration >= maxDuration) {\n                        stopRecording();\n                    }\n                    return newDuration;\n                });\n            }, 1000);\n            // Start audio level monitoring\n            const updateAudioLevel = ()=>{\n                if (recorderRef.current && isRecording) {\n                    setAudioLevel(recorderRef.current.getAudioLevel());\n                    animationRef.current = requestAnimationFrame(updateAudioLevel);\n                }\n            };\n            updateAudioLevel();\n        } catch (err) {\n            console.error('Recording start failed:', err);\n            setError('Failed to start recording. Please check microphone permissions.');\n        }\n    };\n    const stopRecording = async ()=>{\n        if (!recorderRef.current || !isRecording) return;\n        try {\n            const blob = await recorderRef.current.stopRecording();\n            setIsRecording(false);\n            setAudioLevel(0);\n            onRecordingStop === null || onRecordingStop === void 0 ? void 0 : onRecordingStop();\n            if (intervalRef.current) {\n                clearInterval(intervalRef.current);\n            }\n            if (animationRef.current) {\n                cancelAnimationFrame(animationRef.current);\n            }\n            // Clean up previous audio URL\n            if (audioUrl) {\n                URL.revokeObjectURL(audioUrl);\n            }\n            const newAudioUrl = _lib_audio__WEBPACK_IMPORTED_MODULE_2__.audioUtils.createAudioURL(blob);\n            setAudioBlob(blob);\n            setAudioUrl(newAudioUrl);\n            onRecordingComplete === null || onRecordingComplete === void 0 ? void 0 : onRecordingComplete(blob);\n        } catch (err) {\n            setError('Failed to stop recording');\n            console.error('Recording stop failed:', err);\n        }\n    };\n    const playAudio = ()=>{\n        if (!audioRef.current || !audioUrl) return;\n        if (isPlaying) {\n            audioRef.current.pause();\n            setIsPlaying(false);\n        } else {\n            audioRef.current.play();\n            setIsPlaying(true);\n        }\n    };\n    const downloadAudio = ()=>{\n        if (!audioBlob) return;\n        _lib_audio__WEBPACK_IMPORTED_MODULE_2__.audioUtils.downloadAudio(audioBlob, \"voice-recording-\".concat(Date.now(), \".webm\"));\n    };\n    const deleteRecording = ()=>{\n        if (audioUrl) {\n            URL.revokeObjectURL(audioUrl);\n        }\n        setAudioBlob(null);\n        setAudioUrl(null);\n        setDuration(0);\n        setIsPlaying(false);\n    };\n    const handleFileUpload = (event)=>{\n        var _event_target_files;\n        const file = (_event_target_files = event.target.files) === null || _event_target_files === void 0 ? void 0 : _event_target_files[0];\n        if (!file) return;\n        const validation = _lib_audio__WEBPACK_IMPORTED_MODULE_2__.audioUtils.validateAudioFile(file);\n        if (!validation.valid) {\n            setError(validation.error || 'Invalid audio file');\n            return;\n        }\n        setError(null);\n        const blob = new Blob([\n            file\n        ], {\n            type: file.type\n        });\n        const url = _lib_audio__WEBPACK_IMPORTED_MODULE_2__.audioUtils.createAudioURL(blob);\n        if (audioUrl) {\n            URL.revokeObjectURL(audioUrl);\n        }\n        setAudioBlob(blob);\n        setAudioUrl(url);\n        onRecordingComplete === null || onRecordingComplete === void 0 ? void 0 : onRecordingComplete(blob);\n    };\n    const formatTime = (seconds)=>{\n        const mins = Math.floor(seconds / 60);\n        const secs = seconds % 60;\n        return \"\".concat(mins, \":\").concat(secs.toString().padStart(2, '0'));\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6 \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.AnimatePresence, {\n                children: error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: -10\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    exit: {\n                        opacity: 0,\n                        y: -10\n                    },\n                    className: \"bg-red-900/50 border border-red-700 rounded-lg p-4 text-red-200\",\n                    children: error\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                    lineNumber: 242,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                lineNumber: 240,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"card\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold mb-2\",\n                                children: \"Record Your Voice\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                lineNumber: 256,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400\",\n                                children: [\n                                    \"Record at least 30 seconds for best results (max \",\n                                    maxDuration,\n                                    \"s)\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                lineNumber: 257,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                        lineNumber: 255,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-center mb-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"audio-visualizer\",\n                            children: [\n                                ...Array(12)\n                            ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"audio-bar w-3\",\n                                    style: {\n                                        height: isRecording ? \"\".concat(Math.max(4, audioLevel * 60 + Math.random() * 20), \"px\") : '4px'\n                                    }\n                                }, i, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                    lineNumber: 266,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                            lineNumber: 264,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                        lineNumber: 263,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-2xl font-mono text-purple-400\",\n                                children: formatTime(duration)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                lineNumber: 281,\n                                columnNumber: 11\n                            }, this),\n                            maxDuration && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-gray-400\",\n                                children: [\n                                    \"/ \",\n                                    formatTime(maxDuration)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                lineNumber: 285,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                        lineNumber: 280,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-center space-x-4 mb-6\",\n                        children: [\n                            !isRecording ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: startRecording,\n                                className: \"btn-primary flex items-center space-x-2\",\n                                disabled: isRecording,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Mic_Pause_Play_Square_Trash2_Upload_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                        lineNumber: 299,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Start Recording\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                        lineNumber: 300,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                lineNumber: 294,\n                                columnNumber: 13\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: stopRecording,\n                                className: \"bg-red-600 hover:bg-red-700 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-200 flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Mic_Pause_Play_Square_Trash2_Upload_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                        lineNumber: 307,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Stop Recording\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                        lineNumber: 308,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                lineNumber: 303,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"btn-secondary cursor-pointer flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Mic_Pause_Play_Square_Trash2_Upload_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                        lineNumber: 314,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Upload Audio\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                        lineNumber: 315,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"file\",\n                                        accept: \"audio/*\",\n                                        onChange: handleFileUpload,\n                                        className: \"hidden\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                        lineNumber: 316,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                lineNumber: 313,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                        lineNumber: 292,\n                        columnNumber: 9\n                    }, this),\n                    isRecording && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full bg-gray-700 rounded-full h-2 mb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gradient-to-r from-purple-600 to-blue-600 h-2 rounded-full transition-all duration-1000\",\n                            style: {\n                                width: \"\".concat(duration / maxDuration * 100, \"%\")\n                            }\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                            lineNumber: 328,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                        lineNumber: 327,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                lineNumber: 254,\n                columnNumber: 7\n            }, this),\n            audioUrl && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                initial: {\n                    opacity: 0,\n                    y: 20\n                },\n                animate: {\n                    opacity: 1,\n                    y: 0\n                },\n                className: \"card\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: playAudio,\n                                        className: \"btn-secondary flex items-center space-x-2\",\n                                        children: [\n                                            isPlaying ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Mic_Pause_Play_Square_Trash2_Upload_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                                lineNumber: 350,\n                                                columnNumber: 19\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Mic_Pause_Play_Square_Trash2_Upload_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                                lineNumber: 352,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: isPlaying ? 'Pause' : 'Play'\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                                lineNumber: 354,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                        lineNumber: 345,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2 text-gray-400\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Mic_Pause_Play_Square_Trash2_Upload_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                                lineNumber: 358,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Recording ready\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                                lineNumber: 359,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                        lineNumber: 357,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                lineNumber: 344,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: downloadAudio,\n                                        className: \"btn-secondary flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Mic_Pause_Play_Square_Trash2_Upload_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                                lineNumber: 368,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Download\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                                lineNumber: 369,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                        lineNumber: 364,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: deleteRecording,\n                                        className: \"text-red-400 hover:text-red-300 p-2 rounded-lg hover:bg-red-900/20 transition-colors\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Mic_Pause_Play_Square_Trash2_Upload_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                            lineNumber: 376,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                        lineNumber: 372,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                lineNumber: 363,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                        lineNumber: 343,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"audio\", {\n                        ref: audioRef,\n                        src: audioUrl,\n                        onEnded: ()=>setIsPlaying(false),\n                        onPlay: ()=>setIsPlaying(true),\n                        onPause: ()=>setIsPlaying(false),\n                        className: \"hidden\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                        lineNumber: 381,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                lineNumber: 338,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n        lineNumber: 238,\n        columnNumber: 5\n    }, this);\n}\n_s(AudioRecorder, \"0MYx0H1S8d1mQNsKNDWK75M5ObM=\");\n_c = AudioRecorder;\nvar _c;\n$RefreshReg$(_c, \"AudioRecorder\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/AudioRecorder.tsx\n"));

/***/ })

});