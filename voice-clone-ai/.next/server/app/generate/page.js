/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/generate/page";
exports.ids = ["app/generate/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fgenerate%2Fpage&page=%2Fgenerate%2Fpage&appPaths=%2Fgenerate%2Fpage&pagePath=private-next-app-dir%2Fgenerate%2Fpage.tsx&appDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fgenerate%2Fpage&page=%2Fgenerate%2Fpage&appPaths=%2Fgenerate%2Fpage&pagePath=private-next-app-dir%2Fgenerate%2Fpage.tsx&appDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/generate/page.tsx */ \"(rsc)/./src/app/generate/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'generate',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/generate/page\",\n        pathname: \"/generate\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fgenerate%2Fpage&page=%2Fgenerate%2Fpage&appPaths=%2Fgenerate%2Fpage&pagePath=private-next-app-dir%2Fgenerate%2Fpage.tsx&appDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fsrc%2Fapp%2Fgenerate%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fsrc%2Fapp%2Fgenerate%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/generate/page.tsx */ \"(rsc)/./src/app/generate/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGdGFoYWZhcm9vcXVpJTJGRG9jdW1lbnRzJTJGYXVnbWVudC1wcm9qZWN0cyUyRkFJLUFnZW50JTJGdm9pY2UtY2xvbmUtYWklMkZzcmMlMkZhcHAlMkZnZW5lcmF0ZSUyRnBhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxrS0FBcUkiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy90YWhhZmFyb29xdWkvRG9jdW1lbnRzL2F1Z21lbnQtcHJvamVjdHMvQUktQWdlbnQvdm9pY2UtY2xvbmUtYWkvc3JjL2FwcC9nZW5lcmF0ZS9wYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fsrc%2Fapp%2Fgenerate%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIi9Vc2Vycy90YWhhZmFyb29xdWkvRG9jdW1lbnRzL2F1Z21lbnQtcHJvamVjdHMvQUktQWdlbnQvdm9pY2UtY2xvbmUtYWkvc3JjL2FwcC9mYXZpY29uLmljbz9fX25leHRfbWV0YWRhdGFfXyJdLCJzb3VyY2VzQ29udGVudCI6WyIgIGltcG9ydCB7IGZpbGxNZXRhZGF0YVNlZ21lbnQgfSBmcm9tICduZXh0L2Rpc3QvbGliL21ldGFkYXRhL2dldC1tZXRhZGF0YS1yb3V0ZSdcblxuICBleHBvcnQgZGVmYXVsdCBhc3luYyAocHJvcHMpID0+IHtcbiAgICBjb25zdCBpbWFnZURhdGEgPSB7XCJ0eXBlXCI6XCJpbWFnZS94LWljb25cIixcInNpemVzXCI6XCIxNngxNlwifVxuICAgIGNvbnN0IGltYWdlVXJsID0gZmlsbE1ldGFkYXRhU2VnbWVudChcIi5cIiwgYXdhaXQgcHJvcHMucGFyYW1zLCBcImZhdmljb24uaWNvXCIpXG5cbiAgICByZXR1cm4gW3tcbiAgICAgIC4uLmltYWdlRGF0YSxcbiAgICAgIHVybDogaW1hZ2VVcmwgKyBcIlwiLFxuICAgIH1dXG4gIH0iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/generate/page.tsx":
/*!***********************************!*\
  !*** ./src/app/generate/page.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"11f160e9a53d\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyIvVXNlcnMvdGFoYWZhcm9vcXVpL0RvY3VtZW50cy9hdWdtZW50LXByb2plY3RzL0FJLUFnZW50L3ZvaWNlLWNsb25lLWFpL3NyYy9hcHAvZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCIxMWYxNjBlOWE1M2RcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\n\nconst metadata = {\n    title: \"VoiceClone AI - Clone Your Voice in Seconds\",\n    description: \"Create realistic voice clones using advanced AI technology. Generate speech in your own voice from any text.\",\n    keywords: \"voice cloning, AI voice, text to speech, voice synthesis, artificial intelligence\",\n    authors: [\n        {\n            name: \"VoiceClone AI\"\n        }\n    ],\n    openGraph: {\n        title: \"VoiceClone AI - Clone Your Voice in Seconds\",\n        description: \"Create realistic voice clones using advanced AI technology\",\n        type: \"website\",\n        url: \"https://voiceclone-ai.vercel.app\"\n    },\n    twitter: {\n        card: \"summary_large_image\",\n        title: \"VoiceClone AI - Clone Your Voice in Seconds\",\n        description: \"Create realistic voice clones using advanced AI technology\"\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        className: \"dark\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default().className)} antialiased bg-black text-white min-h-screen`,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative min-h-screen\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed inset-0 bg-gradient-to-br from-purple-900/20 via-black to-blue-900/20 pointer-events-none\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/layout.tsx\",\n                        lineNumber: 35,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative z-10\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/layout.tsx\",\n                        lineNumber: 38,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/layout.tsx\",\n                lineNumber: 33,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/layout.tsx\",\n            lineNumber: 32,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/layout.tsx\",\n        lineNumber: 31,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fsrc%2Fapp%2Fgenerate%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fsrc%2Fapp%2Fgenerate%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/generate/page.tsx */ \"(ssr)/./src/app/generate/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGdGFoYWZhcm9vcXVpJTJGRG9jdW1lbnRzJTJGYXVnbWVudC1wcm9qZWN0cyUyRkFJLUFnZW50JTJGdm9pY2UtY2xvbmUtYWklMkZzcmMlMkZhcHAlMkZnZW5lcmF0ZSUyRnBhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxrS0FBcUkiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy90YWhhZmFyb29xdWkvRG9jdW1lbnRzL2F1Z21lbnQtcHJvamVjdHMvQUktQWdlbnQvdm9pY2UtY2xvbmUtYWkvc3JjL2FwcC9nZW5lcmF0ZS9wYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fsrc%2Fapp%2Fgenerate%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/generate/page.tsx":
/*!***********************************!*\
  !*** ./src/app/generate/page.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ GeneratePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Download_Mic_Pause_Play_Settings_Sparkles_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Download,Mic,Pause,Play,Settings,Sparkles,Volume2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Download_Mic_Pause_Play_Settings_Sparkles_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Download,Mic,Pause,Play,Settings,Sparkles,Volume2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Download_Mic_Pause_Play_Settings_Sparkles_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Download,Mic,Pause,Play,Settings,Sparkles,Volume2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/mic.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Download_Mic_Pause_Play_Settings_Sparkles_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Download,Mic,Pause,Play,Settings,Sparkles,Volume2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Download_Mic_Pause_Play_Settings_Sparkles_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Download,Mic,Pause,Play,Settings,Sparkles,Volume2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/volume-2.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Download_Mic_Pause_Play_Settings_Sparkles_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Download,Mic,Pause,Play,Settings,Sparkles,Volume2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/pause.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Download_Mic_Pause_Play_Settings_Sparkles_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Download,Mic,Pause,Play,Settings,Sparkles,Volume2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Download_Mic_Pause_Play_Settings_Sparkles_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Download,Mic,Pause,Play,Settings,Sparkles,Volume2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _lib_localStorage__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/localStorage */ \"(ssr)/./src/lib/localStorage.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nfunction GeneratePage() {\n    const [voiceModels, setVoiceModels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedVoice, setSelectedVoice] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [text, setText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isGenerating, setIsGenerating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isPlaying, setIsPlaying] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [audioUrl, setAudioUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showSettings, setShowSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [settings, setSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        stability: 0.5,\n        similarity_boost: 0.5,\n        style: 0.0,\n        use_speaker_boost: true\n    });\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [coquiServerReady, setCoquiServerReady] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [manualServerReady, setManualServerReady] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const audioRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"GeneratePage.useEffect\": ()=>{\n            fetchVoiceModels();\n        }\n    }[\"GeneratePage.useEffect\"], []);\n    const fetchVoiceModels = async ()=>{\n        try {\n            // Check if Coqui TTS server is running via proxy\n            let serverRunning = false;\n            try {\n                const response = await fetch('http://localhost:3001/api/tts?text=test', {\n                    method: 'GET'\n                });\n                serverRunning = response.ok || response.status === 200;\n                setCoquiServerReady(serverRunning);\n                console.log('Coqui TTS server status via proxy:', serverRunning);\n            } catch (error) {\n                console.log('Coqui TTS server not accessible via proxy:', error);\n                setCoquiServerReady(false);\n            }\n            // Get voice models from localStorage\n            const localModels = _lib_localStorage__WEBPACK_IMPORTED_MODULE_3__.localStorageService.getVoiceModels();\n            let allVoiceModels = [];\n            if (localModels.length > 0) {\n                // Convert local models to the expected format\n                allVoiceModels = localModels.map((model)=>({\n                        ...model,\n                        updated_at: model.created_at\n                    }));\n            }\n            // Always add a default voice model for users who haven't created any clones yet\n            const defaultVoice = {\n                id: 'demo-voice-1',\n                name: 'Default Voice (Coqui TTS)',\n                description: 'Default Coqui TTS voice using Tacotron2',\n                voice_id: 'coqui-demo',\n                created_at: new Date().toISOString()\n            };\n            // Add default voice if no custom voices exist, or if no voice with that ID exists\n            if (!allVoiceModels.some((v)=>v.id === 'demo-voice-1')) {\n                allVoiceModels.unshift(defaultVoice);\n            }\n            setVoiceModels(allVoiceModels);\n            if (allVoiceModels.length > 0) {\n                setSelectedVoice(allVoiceModels[0]);\n            }\n        } catch (error) {\n            console.error('Failed to fetch voice models:', error);\n            setError('Failed to load voice models');\n        }\n    };\n    const generateSpeech = async ()=>{\n        if (!selectedVoice || !text.trim()) return;\n        setIsGenerating(true);\n        setError(null);\n        try {\n            // Call Coqui TTS server via our CORS proxy\n            console.log('Generating speech with text:', text.trim());\n            console.log('Using voice model:', selectedVoice);\n            const encodedText = encodeURIComponent(text.trim());\n            let proxyUrl = `http://localhost:3001/api/tts?text=${encodedText}`;\n            // Add voice cloning parameters if available\n            if (selectedVoice.audio_data && selectedVoice.type === 'coqui') {\n                try {\n                    console.log('Uploading voice sample for cloning:', selectedVoice.name);\n                    // Upload the voice sample to our proxy server\n                    const uploadResponse = await fetch('http://localhost:3001/upload-voice', {\n                        method: 'POST',\n                        headers: {\n                            'Content-Type': 'application/json'\n                        },\n                        body: JSON.stringify({\n                            audioData: selectedVoice.audio_data,\n                            filename: `voice_${selectedVoice.id}.webm`\n                        })\n                    });\n                    if (uploadResponse.ok) {\n                        const uploadResult = await uploadResponse.json();\n                        const styleWavUrl = uploadResult.audioUrl;\n                        proxyUrl += `&style_wav=${encodeURIComponent(styleWavUrl)}`;\n                        console.log('Using voice cloning with style_wav:', styleWavUrl);\n                    } else {\n                        console.error('Failed to upload voice sample');\n                    }\n                } catch (error) {\n                    console.error('Error uploading voice sample:', error);\n                }\n            }\n            console.log('Calling Coqui TTS via proxy:', proxyUrl);\n            const response = await fetch(proxyUrl, {\n                method: 'GET',\n                headers: {\n                    'Accept': 'audio/wav'\n                }\n            });\n            if (!response.ok) {\n                throw new Error(`Coqui TTS failed: ${response.status} - ${response.statusText}`);\n            }\n            // Get audio blob from response\n            const audioBlob = await response.blob();\n            // Clean up previous audio URL\n            if (audioUrl) {\n                URL.revokeObjectURL(audioUrl);\n            }\n            // Create new audio URL\n            const newAudioUrl = URL.createObjectURL(audioBlob);\n            setAudioUrl(newAudioUrl);\n            console.log('Speech generated successfully!');\n        } catch (error) {\n            console.error('Speech generation failed:', error);\n            setError(error instanceof Error ? error.message : 'Failed to generate speech');\n        } finally{\n            setIsGenerating(false);\n        }\n    };\n    const playAudio = ()=>{\n        if (!audioRef.current || !audioUrl) return;\n        if (isPlaying) {\n            audioRef.current.pause();\n            setIsPlaying(false);\n        } else {\n            audioRef.current.play();\n            setIsPlaying(true);\n        }\n    };\n    const downloadAudio = ()=>{\n        if (!audioUrl) return;\n        const a = document.createElement('a');\n        a.href = audioUrl;\n        a.download = `voice-clone-${Date.now()}.mp3`;\n        document.body.appendChild(a);\n        a.click();\n        document.body.removeChild(a);\n    };\n    const sampleTexts = [\n        \"Hello, this is my AI voice clone speaking. How do I sound?\",\n        \"Welcome to the future of voice technology. Your voice, unlimited possibilities.\",\n        \"I can read any text you give me in your own voice. Pretty amazing, right?\",\n        \"The quick brown fox jumps over the lazy dog. This sentence contains every letter of the alphabet.\"\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen py-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"fixed top-0 w-full z-50 bg-black/80 backdrop-blur-md border-b border-gray-800\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center h-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/\",\n                                className: \"flex items-center space-x-2 text-gray-300 hover:text-white transition-colors\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Download_Mic_Pause_Play_Settings_Sparkles_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                        lineNumber: 217,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Back to Home\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                        lineNumber: 218,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                lineNumber: 216,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Download_Mic_Pause_Play_Settings_Sparkles_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"w-8 h-8 text-purple-400\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                        lineNumber: 221,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xl font-bold gradient-text\",\n                                        children: \"VoiceClone AI\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                        lineNumber: 222,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                lineNumber: 220,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                        lineNumber: 215,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                    lineNumber: 214,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                lineNumber: 213,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"pt-24 px-4 sm:px-6 lg:px-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-6xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-4xl md:text-5xl font-bold mb-4\",\n                                    children: [\n                                        \"Generate \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"gradient-text\",\n                                            children: \"Speech\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                            lineNumber: 232,\n                                            columnNumber: 24\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                    lineNumber: 231,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl text-gray-300 mb-4\",\n                                    children: \"Type any text and hear it spoken in your cloned voice using Coqui TTS\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                    lineNumber: 234,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"inline-flex items-center px-4 py-2 bg-blue-900/30 border border-blue-700 rounded-lg text-blue-300\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm\",\n                                        children: \"\\uD83D\\uDE80 Powered by Coqui TTS - Free & Open Source\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                        lineNumber: 238,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                    lineNumber: 237,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                            lineNumber: 230,\n                            columnNumber: 11\n                        }, this),\n                        error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: -10\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            className: \"bg-red-900/50 border border-red-700 rounded-lg p-4 text-red-200 mb-6 max-w-2xl mx-auto\",\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                            lineNumber: 243,\n                            columnNumber: 13\n                        }, this),\n                        !coquiServerReady && !manualServerReady && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"card\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xl font-semibold mb-4\",\n                                        children: \"Coqui TTS Server Required\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                        lineNumber: 256,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-300 mb-4\",\n                                        children: [\n                                            \"To use voice cloning, you need to start the Coqui TTS server. The server is currently \",\n                                            coquiServerReady ? 'running' : 'not running',\n                                            \".\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                        lineNumber: 257,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-400 mb-4\",\n                                        children: \"If you have the Coqui TTS server running on port 5002, click the button below:\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                        lineNumber: 261,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>{\n                                            setManualServerReady(true);\n                                            setCoquiServerReady(true);\n                                            fetchVoiceModels();\n                                        },\n                                        className: \"btn-primary\",\n                                        children: \"Mark Server as Ready\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                        lineNumber: 264,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                lineNumber: 255,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                            lineNumber: 254,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid lg:grid-cols-3 gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"lg:col-span-1\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"card\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-xl font-semibold mb-4 flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Download_Mic_Pause_Play_Settings_Sparkles_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        className: \"w-5 h-5 mr-2 text-purple-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                        lineNumber: 283,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Select Voice\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                lineNumber: 282,\n                                                columnNumber: 17\n                                            }, this),\n                                            voiceModels.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center py-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-400 mb-4\",\n                                                        children: \"No voice models found\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                        lineNumber: 289,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: \"/record\",\n                                                        className: \"btn-primary\",\n                                                        children: \"Create Voice Clone\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                        lineNumber: 290,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                lineNumber: 288,\n                                                columnNumber: 19\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: voiceModels.map((voice)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setSelectedVoice(voice),\n                                                        className: `w-full p-4 rounded-lg border transition-all duration-200 text-left ${selectedVoice?.id === voice.id ? 'border-purple-500 bg-purple-900/20' : 'border-gray-700 hover:border-gray-600'}`,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"font-medium\",\n                                                                children: voice.name\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                                lineNumber: 306,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            voice.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-gray-400 mt-1\",\n                                                                children: voice.description\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                                lineNumber: 308,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-gray-500 mt-2\",\n                                                                children: [\n                                                                    \"Created \",\n                                                                    new Date(voice.created_at).toLocaleDateString()\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                                lineNumber: 312,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, voice.id, true, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                        lineNumber: 297,\n                                                        columnNumber: 23\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                lineNumber: 295,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setShowSettings(!showSettings),\n                                                        className: \"btn-secondary w-full flex items-center justify-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Download_Mic_Pause_Play_Settings_Sparkles_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                className: \"w-4 h-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                                lineNumber: 326,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"Voice Settings\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                                lineNumber: 327,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                        lineNumber: 322,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    showSettings && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                                        initial: {\n                                                            opacity: 0,\n                                                            height: 0\n                                                        },\n                                                        animate: {\n                                                            opacity: 1,\n                                                            height: 'auto'\n                                                        },\n                                                        className: \"mt-4 space-y-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium mb-2\",\n                                                                        children: [\n                                                                            \"Stability: \",\n                                                                            settings.stability.toFixed(2)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                                        lineNumber: 337,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"range\",\n                                                                        min: \"0\",\n                                                                        max: \"1\",\n                                                                        step: \"0.01\",\n                                                                        value: settings.stability,\n                                                                        onChange: (e)=>setSettings((prev)=>({\n                                                                                    ...prev,\n                                                                                    stability: parseFloat(e.target.value)\n                                                                                })),\n                                                                        className: \"w-full\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                                        lineNumber: 340,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                                lineNumber: 336,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium mb-2\",\n                                                                        children: [\n                                                                            \"Similarity: \",\n                                                                            settings.similarity_boost.toFixed(2)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                                        lineNumber: 352,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"range\",\n                                                                        min: \"0\",\n                                                                        max: \"1\",\n                                                                        step: \"0.01\",\n                                                                        value: settings.similarity_boost,\n                                                                        onChange: (e)=>setSettings((prev)=>({\n                                                                                    ...prev,\n                                                                                    similarity_boost: parseFloat(e.target.value)\n                                                                                })),\n                                                                        className: \"w-full\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                                        lineNumber: 355,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                                lineNumber: 351,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium mb-2\",\n                                                                        children: [\n                                                                            \"Style: \",\n                                                                            settings.style.toFixed(2)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                                        lineNumber: 367,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"range\",\n                                                                        min: \"0\",\n                                                                        max: \"1\",\n                                                                        step: \"0.01\",\n                                                                        value: settings.style,\n                                                                        onChange: (e)=>setSettings((prev)=>({\n                                                                                    ...prev,\n                                                                                    style: parseFloat(e.target.value)\n                                                                                })),\n                                                                        className: \"w-full\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                                        lineNumber: 370,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                                lineNumber: 366,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"checkbox\",\n                                                                        id: \"speaker-boost\",\n                                                                        checked: settings.use_speaker_boost,\n                                                                        onChange: (e)=>setSettings((prev)=>({\n                                                                                    ...prev,\n                                                                                    use_speaker_boost: e.target.checked\n                                                                                })),\n                                                                        className: \"rounded\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                                        lineNumber: 382,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        htmlFor: \"speaker-boost\",\n                                                                        className: \"text-sm\",\n                                                                        children: \"Speaker Boost\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                                        lineNumber: 389,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                                lineNumber: 381,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                        lineNumber: 331,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                lineNumber: 321,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                        lineNumber: 281,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                    lineNumber: 280,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"lg:col-span-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"card\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-xl font-semibold mb-4\",\n                                                children: \"Enter Text\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                lineNumber: 402,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                value: text,\n                                                onChange: (e)=>setText(e.target.value),\n                                                placeholder: \"Type the text you want to convert to speech...\",\n                                                className: \"input-field w-full h-40 resize-none mb-4\",\n                                                maxLength: 5000\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                lineNumber: 404,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-center mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-400\",\n                                                        children: [\n                                                            text.length,\n                                                            \"/5000 characters\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                        lineNumber: 413,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-400\",\n                                                        children: [\n                                                            \"Estimated: ~\",\n                                                            Math.ceil(text.length / 10),\n                                                            \" seconds\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                        lineNumber: 416,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                lineNumber: 412,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"text-sm font-medium mb-3\",\n                                                        children: \"Quick Samples:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                        lineNumber: 423,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-2\",\n                                                        children: sampleTexts.map((sample, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>setText(sample),\n                                                                className: \"text-left p-3 rounded-lg border border-gray-700 hover:border-gray-600 transition-colors text-sm\",\n                                                                children: [\n                                                                    '\"',\n                                                                    sample.substring(0, 50),\n                                                                    '...\"'\n                                                                ]\n                                                            }, index, true, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                                lineNumber: 426,\n                                                                columnNumber: 23\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                        lineNumber: 424,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                lineNumber: 422,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: generateSpeech,\n                                                disabled: !selectedVoice || !text.trim() || isGenerating || !coquiServerReady,\n                                                className: \"btn-primary w-full flex items-center justify-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                                children: isGenerating ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"animate-spin rounded-full h-5 w-5 border-b-2 border-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                            lineNumber: 445,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Generating with Coqui TTS...\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                            lineNumber: 446,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true) : !coquiServerReady ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Download_Mic_Pause_Play_Settings_Sparkles_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                            className: \"w-5 h-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                            lineNumber: 450,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Start Coqui Server First\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                            lineNumber: 451,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Download_Mic_Pause_Play_Settings_Sparkles_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                            className: \"w-5 h-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                            lineNumber: 455,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Generate Speech\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                            lineNumber: 456,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                lineNumber: 438,\n                                                columnNumber: 17\n                                            }, this),\n                                            !coquiServerReady && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-yellow-400 text-center\",\n                                                children: \"⚠️ Coqui TTS server is required for speech generation\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                lineNumber: 462,\n                                                columnNumber: 19\n                                            }, this),\n                                            audioUrl && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                                initial: {\n                                                    opacity: 0,\n                                                    y: 20\n                                                },\n                                                animate: {\n                                                    opacity: 1,\n                                                    y: 0\n                                                },\n                                                className: \"mt-6 p-4 bg-gray-800/50 rounded-lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: playAudio,\n                                                                        className: \"btn-secondary flex items-center space-x-2\",\n                                                                        children: [\n                                                                            isPlaying ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Download_Mic_Pause_Play_Settings_Sparkles_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                                className: \"w-4 h-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                                                lineNumber: 481,\n                                                                                columnNumber: 29\n                                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Download_Mic_Pause_Play_Settings_Sparkles_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                                className: \"w-4 h-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                                                lineNumber: 483,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: isPlaying ? 'Pause' : 'Play'\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                                                lineNumber: 485,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                                        lineNumber: 476,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm text-gray-400\",\n                                                                        children: \"Generated speech ready\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                                        lineNumber: 488,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                                lineNumber: 475,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: downloadAudio,\n                                                                className: \"btn-secondary flex items-center space-x-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Download_Mic_Pause_Play_Settings_Sparkles_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                        className: \"w-4 h-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                                        lineNumber: 497,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"Download\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                                        lineNumber: 498,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                                lineNumber: 493,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                        lineNumber: 474,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"audio\", {\n                                                        ref: audioRef,\n                                                        src: audioUrl,\n                                                        onEnded: ()=>setIsPlaying(false),\n                                                        onPlay: ()=>setIsPlaying(true),\n                                                        onPause: ()=>setIsPlaying(false),\n                                                        className: \"hidden\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                        lineNumber: 502,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                                lineNumber: 469,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                        lineNumber: 401,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                                    lineNumber: 400,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                            lineNumber: 278,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                    lineNumber: 229,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n                lineNumber: 228,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/generate/page.tsx\",\n        lineNumber: 211,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/generate/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/localStorage.ts":
/*!*********************************!*\
  !*** ./src/lib/localStorage.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   localStorageService: () => (/* binding */ localStorageService)\n/* harmony export */ });\n// Local storage utilities for voice models\n// This serves as a fallback when Supabase is not available\nconst VOICE_MODELS_KEY = 'voice_clone_models';\nconst localStorageService = {\n    // Get all voice models from localStorage\n    getVoiceModels () {\n        if (true) return [];\n        try {\n            const stored = localStorage.getItem(VOICE_MODELS_KEY);\n            return stored ? JSON.parse(stored) : [];\n        } catch (error) {\n            console.error('Error reading voice models from localStorage:', error);\n            return [];\n        }\n    },\n    // Save a voice model to localStorage\n    saveVoiceModel (voiceModel) {\n        if (true) return;\n        try {\n            const existing = this.getVoiceModels();\n            const updated = [\n                ...existing,\n                voiceModel\n            ];\n            localStorage.setItem(VOICE_MODELS_KEY, JSON.stringify(updated));\n        } catch (error) {\n            console.error('Error saving voice model to localStorage:', error);\n        }\n    },\n    // Remove a voice model from localStorage\n    removeVoiceModel (voiceId) {\n        if (true) return;\n        try {\n            const existing = this.getVoiceModels();\n            const filtered = existing.filter((model)=>model.voice_id !== voiceId);\n            localStorage.setItem(VOICE_MODELS_KEY, JSON.stringify(filtered));\n        } catch (error) {\n            console.error('Error removing voice model from localStorage:', error);\n        }\n    },\n    // Clear all voice models\n    clearVoiceModels () {\n        if (true) return;\n        try {\n            localStorage.removeItem(VOICE_MODELS_KEY);\n        } catch (error) {\n            console.error('Error clearing voice models from localStorage:', error);\n        }\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/localStorage.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/framer-motion","vendor-chunks/motion-dom","vendor-chunks/lucide-react","vendor-chunks/motion-utils","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fgenerate%2Fpage&page=%2Fgenerate%2Fpage&appPaths=%2Fgenerate%2Fpage&pagePath=private-next-app-dir%2Fgenerate%2Fpage.tsx&appDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();