/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/record/page";
exports.ids = ["app/record/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Frecord%2Fpage&page=%2Frecord%2Fpage&appPaths=%2Frecord%2Fpage&pagePath=private-next-app-dir%2Frecord%2Fpage.tsx&appDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Frecord%2Fpage&page=%2Frecord%2Fpage&appPaths=%2Frecord%2Fpage&pagePath=private-next-app-dir%2Frecord%2Fpage.tsx&appDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/record/page.tsx */ \"(rsc)/./src/app/record/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'record',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/record/page\",\n        pathname: \"/record\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Frecord%2Fpage&page=%2Frecord%2Fpage&appPaths=%2Frecord%2Fpage&pagePath=private-next-app-dir%2Frecord%2Fpage.tsx&appDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fsrc%2Fapp%2Frecord%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fsrc%2Fapp%2Frecord%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/record/page.tsx */ \"(rsc)/./src/app/record/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGdGFoYWZhcm9vcXVpJTJGRG9jdW1lbnRzJTJGYXVnbWVudC1wcm9qZWN0cyUyRkFJLUFnZW50JTJGdm9pY2UtY2xvbmUtYWklMkZzcmMlMkZhcHAlMkZyZWNvcmQlMkZwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsOEpBQW1JIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvdGFoYWZhcm9vcXVpL0RvY3VtZW50cy9hdWdtZW50LXByb2plY3RzL0FJLUFnZW50L3ZvaWNlLWNsb25lLWFpL3NyYy9hcHAvcmVjb3JkL3BhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fsrc%2Fapp%2Frecord%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIi9Vc2Vycy90YWhhZmFyb29xdWkvRG9jdW1lbnRzL2F1Z21lbnQtcHJvamVjdHMvQUktQWdlbnQvdm9pY2UtY2xvbmUtYWkvc3JjL2FwcC9mYXZpY29uLmljbz9fX25leHRfbWV0YWRhdGFfXyJdLCJzb3VyY2VzQ29udGVudCI6WyIgIGltcG9ydCB7IGZpbGxNZXRhZGF0YVNlZ21lbnQgfSBmcm9tICduZXh0L2Rpc3QvbGliL21ldGFkYXRhL2dldC1tZXRhZGF0YS1yb3V0ZSdcblxuICBleHBvcnQgZGVmYXVsdCBhc3luYyAocHJvcHMpID0+IHtcbiAgICBjb25zdCBpbWFnZURhdGEgPSB7XCJ0eXBlXCI6XCJpbWFnZS94LWljb25cIixcInNpemVzXCI6XCIxNngxNlwifVxuICAgIGNvbnN0IGltYWdlVXJsID0gZmlsbE1ldGFkYXRhU2VnbWVudChcIi5cIiwgYXdhaXQgcHJvcHMucGFyYW1zLCBcImZhdmljb24uaWNvXCIpXG5cbiAgICByZXR1cm4gW3tcbiAgICAgIC4uLmltYWdlRGF0YSxcbiAgICAgIHVybDogaW1hZ2VVcmwgKyBcIlwiLFxuICAgIH1dXG4gIH0iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"11f160e9a53d\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyIvVXNlcnMvdGFoYWZhcm9vcXVpL0RvY3VtZW50cy9hdWdtZW50LXByb2plY3RzL0FJLUFnZW50L3ZvaWNlLWNsb25lLWFpL3NyYy9hcHAvZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCIxMWYxNjBlOWE1M2RcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\n\nconst metadata = {\n    title: \"VoiceClone AI - Clone Your Voice in Seconds\",\n    description: \"Create realistic voice clones using advanced AI technology. Generate speech in your own voice from any text.\",\n    keywords: \"voice cloning, AI voice, text to speech, voice synthesis, artificial intelligence\",\n    authors: [\n        {\n            name: \"VoiceClone AI\"\n        }\n    ],\n    openGraph: {\n        title: \"VoiceClone AI - Clone Your Voice in Seconds\",\n        description: \"Create realistic voice clones using advanced AI technology\",\n        type: \"website\",\n        url: \"https://voiceclone-ai.vercel.app\"\n    },\n    twitter: {\n        card: \"summary_large_image\",\n        title: \"VoiceClone AI - Clone Your Voice in Seconds\",\n        description: \"Create realistic voice clones using advanced AI technology\"\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        className: \"dark\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default().className)} antialiased bg-black text-white min-h-screen`,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative min-h-screen\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed inset-0 bg-gradient-to-br from-purple-900/20 via-black to-blue-900/20 pointer-events-none\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/layout.tsx\",\n                        lineNumber: 35,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative z-10\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/layout.tsx\",\n                        lineNumber: 38,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/layout.tsx\",\n                lineNumber: 33,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/layout.tsx\",\n            lineNumber: 32,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/layout.tsx\",\n        lineNumber: 31,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/record/page.tsx":
/*!*********************************!*\
  !*** ./src/app/record/page.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx",
"default",
));


/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fsrc%2Fapp%2Frecord%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fsrc%2Fapp%2Frecord%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/record/page.tsx */ \"(ssr)/./src/app/record/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGdGFoYWZhcm9vcXVpJTJGRG9jdW1lbnRzJTJGYXVnbWVudC1wcm9qZWN0cyUyRkFJLUFnZW50JTJGdm9pY2UtY2xvbmUtYWklMkZzcmMlMkZhcHAlMkZyZWNvcmQlMkZwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsOEpBQW1JIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvdGFoYWZhcm9vcXVpL0RvY3VtZW50cy9hdWdtZW50LXByb2plY3RzL0FJLUFnZW50L3ZvaWNlLWNsb25lLWFpL3NyYy9hcHAvcmVjb3JkL3BhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fsrc%2Fapp%2Frecord%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/record/page.tsx":
/*!*********************************!*\
  !*** ./src/app/record/page.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RecordPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_AudioWaveform_Mic_Sparkles_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,AudioWaveform,Mic,Sparkles,Volume2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_AudioWaveform_Mic_Sparkles_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,AudioWaveform,Mic,Sparkles,Volume2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_AudioWaveform_Mic_Sparkles_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,AudioWaveform,Mic,Sparkles,Volume2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_AudioWaveform_Mic_Sparkles_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,AudioWaveform,Mic,Sparkles,Volume2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/mic.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_AudioWaveform_Mic_Sparkles_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,AudioWaveform,Mic,Sparkles,Volume2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/audio-waveform.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_AudioWaveform_Mic_Sparkles_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,AudioWaveform,Mic,Sparkles,Volume2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/volume-2.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_AudioRecorder__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/AudioRecorder */ \"(ssr)/./src/components/AudioRecorder.tsx\");\n/* harmony import */ var _lib_localStorage__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/localStorage */ \"(ssr)/./src/lib/localStorage.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\nfunction RecordPage() {\n    const [step, setStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [voiceName, setVoiceName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [voiceDescription, setVoiceDescription] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [audioBlob, setAudioBlob] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isProcessing, setIsProcessing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleRecordingComplete = (blob)=>{\n        setAudioBlob(blob);\n    };\n    const handleNext = ()=>{\n        if (step === 1 && voiceName.trim()) {\n            setStep(2);\n        } else if (step === 2 && audioBlob) {\n            setStep(3);\n        }\n    };\n    const handleCreateVoiceClone = async ()=>{\n        if (!audioBlob || !voiceName.trim()) return;\n        setIsProcessing(true);\n        try {\n            console.log('Creating voice clone...', {\n                voiceName,\n                voiceDescription,\n                audioBlob\n            });\n            // Create FormData for the API call\n            const formData = new FormData();\n            formData.append('name', voiceName.trim());\n            formData.append('description', voiceDescription.trim() || `Voice clone: ${voiceName}`);\n            // Ensure the audio blob has the correct type and filename\n            let filename = 'voice-sample.webm';\n            let audioFile = audioBlob;\n            // If the blob doesn't have a proper MIME type, create a new one\n            if (!audioBlob.type || !audioBlob.type.startsWith('audio/')) {\n                console.log('Original blob type:', audioBlob.type);\n                audioFile = new Blob([\n                    audioBlob\n                ], {\n                    type: 'audio/webm'\n                });\n                console.log('Updated blob type:', audioFile.type);\n            }\n            // Determine filename based on MIME type\n            if (audioFile.type.includes('webm')) {\n                filename = 'voice-sample.webm';\n            } else if (audioFile.type.includes('mp4')) {\n                filename = 'voice-sample.mp4';\n            } else if (audioFile.type.includes('wav')) {\n                filename = 'voice-sample.wav';\n            } else if (audioFile.type.includes('ogg')) {\n                filename = 'voice-sample.ogg';\n            }\n            formData.append('audio', audioFile, filename);\n            // Call the Coqui TTS voice cloning API\n            const response = await fetch('/api/coqui-voice-clone', {\n                method: 'POST',\n                body: formData\n            });\n            const result = await response.json();\n            if (!response.ok) {\n                throw new Error(result.error || 'Failed to create voice clone');\n            }\n            console.log('Voice clone created successfully:', result);\n            // Save to localStorage as backup\n            if (result.voiceModel) {\n                _lib_localStorage__WEBPACK_IMPORTED_MODULE_4__.localStorageService.saveVoiceModel({\n                    id: result.voiceModel.id,\n                    name: result.voiceModel.name,\n                    description: result.voiceModel.description,\n                    voice_id: result.voiceModel.voice_id,\n                    created_at: result.voiceModel.created_at || new Date().toISOString()\n                });\n            }\n            // Redirect to generation page\n            window.location.href = '/generate';\n        } catch (error) {\n            console.error('Voice cloning failed:', error);\n            // Show error message to user\n            let errorMessage = 'Failed to create voice clone. ';\n            if (error instanceof Error) {\n                if (error.message.includes('quota')) {\n                    errorMessage += 'API quota exceeded. Please try again later.';\n                } else if (error.message.includes('unauthorized')) {\n                    errorMessage += 'API configuration error. Please check settings.';\n                } else {\n                    errorMessage += error.message;\n                }\n            } else {\n                errorMessage += 'Please try again.';\n            }\n            alert(errorMessage) // For now, we'll use alert. In production, you'd want a proper toast/notification\n            ;\n        } finally{\n            setIsProcessing(false);\n        }\n    };\n    const steps = [\n        {\n            number: 1,\n            title: 'Voice Details',\n            description: 'Name your voice clone'\n        },\n        {\n            number: 2,\n            title: 'Record Audio',\n            description: 'Capture your voice'\n        },\n        {\n            number: 3,\n            title: 'Create Clone',\n            description: 'Generate your AI voice'\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen py-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"fixed top-0 w-full z-50 bg-black/80 backdrop-blur-md border-b border-gray-800\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center h-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/\",\n                                className: \"flex items-center space-x-2 text-gray-300 hover:text-white transition-colors\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_AudioWaveform_Mic_Sparkles_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                        lineNumber: 128,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Back to Home\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                        lineNumber: 129,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                lineNumber: 127,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_AudioWaveform_Mic_Sparkles_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"w-8 h-8 text-purple-400\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                        lineNumber: 132,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xl font-bold gradient-text\",\n                                        children: \"VoiceClone AI\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                        lineNumber: 133,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                lineNumber: 131,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                        lineNumber: 126,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                    lineNumber: 125,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                lineNumber: 124,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"pt-24 px-4 sm:px-6 lg:px-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-4xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-12\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center space-x-8\",\n                                children: steps.map((stepItem, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: `w-12 h-12 rounded-full flex items-center justify-center font-semibold transition-all duration-300 ${step >= stepItem.number ? 'bg-gradient-to-r from-purple-600 to-blue-600 text-white' : 'bg-gray-800 text-gray-400'}`,\n                                                        children: stepItem.number\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                                        lineNumber: 147,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mt-2 text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm font-medium\",\n                                                                children: stepItem.title\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                                                lineNumber: 157,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-gray-400\",\n                                                                children: stepItem.description\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                                                lineNumber: 158,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                                        lineNumber: 156,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                                lineNumber: 146,\n                                                columnNumber: 19\n                                            }, this),\n                                            index < steps.length - 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: `w-16 h-1 mx-4 transition-all duration-300 ${step > stepItem.number ? 'bg-gradient-to-r from-purple-600 to-blue-600' : 'bg-gray-800'}`\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                                lineNumber: 162,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, stepItem.number, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                        lineNumber: 145,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                lineNumber: 143,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                            lineNumber: 142,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                x: 20\n                            },\n                            animate: {\n                                opacity: 1,\n                                x: 0\n                            },\n                            exit: {\n                                opacity: 0,\n                                x: -20\n                            },\n                            transition: {\n                                duration: 0.3\n                            },\n                            children: [\n                                step === 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card max-w-2xl mx-auto\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center mb-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"text-3xl font-bold mb-4\",\n                                                    children: [\n                                                        \"Create Your \",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"gradient-text\",\n                                                            children: \"Voice Clone\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                                            lineNumber: 187,\n                                                            columnNumber: 33\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                                    lineNumber: 186,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-300\",\n                                                    children: \"Let's start by giving your voice clone a name and description\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                                    lineNumber: 189,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                            lineNumber: 185,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium mb-2\",\n                                                            children: \"Voice Name *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                                            lineNumber: 196,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            value: voiceName,\n                                                            onChange: (e)=>setVoiceName(e.target.value),\n                                                            placeholder: \"e.g., My Professional Voice\",\n                                                            className: \"input-field w-full\",\n                                                            maxLength: 50\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                                            lineNumber: 199,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-gray-400 mt-1\",\n                                                            children: [\n                                                                voiceName.length,\n                                                                \"/50 characters\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                                            lineNumber: 207,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                                    lineNumber: 195,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium mb-2\",\n                                                            children: \"Description (Optional)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                                            lineNumber: 213,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                            value: voiceDescription,\n                                                            onChange: (e)=>setVoiceDescription(e.target.value),\n                                                            placeholder: \"Describe the tone, style, or intended use of this voice...\",\n                                                            className: \"input-field w-full h-24 resize-none\",\n                                                            maxLength: 200\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                                            lineNumber: 216,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-gray-400 mt-1\",\n                                                            children: [\n                                                                voiceDescription.length,\n                                                                \"/200 characters\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                                            lineNumber: 223,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                                    lineNumber: 212,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: handleNext,\n                                                    disabled: !voiceName.trim(),\n                                                    className: \"btn-primary w-full flex items-center justify-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Continue to Recording\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                                            lineNumber: 233,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_AudioWaveform_Mic_Sparkles_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                            className: \"w-5 h-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                                            lineNumber: 234,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                                    lineNumber: 228,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                            lineNumber: 194,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                    lineNumber: 184,\n                                    columnNumber: 15\n                                }, this),\n                                step === 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"max-w-3xl mx-auto\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center mb-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"text-3xl font-bold mb-4\",\n                                                    children: [\n                                                        \"Record Your \",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"gradient-text\",\n                                                            children: \"Voice Sample\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                                            lineNumber: 244,\n                                                            columnNumber: 33\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                                    lineNumber: 243,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-300 max-w-2xl mx-auto\",\n                                                    children: \"Record at least 30 seconds of clear speech. Read naturally and avoid background noise for the best results.\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                                    lineNumber: 246,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                            lineNumber: 242,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AudioRecorder__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            onRecordingComplete: handleRecordingComplete,\n                                            maxDuration: 120,\n                                            className: \"mb-8\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                            lineNumber: 251,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setStep(1),\n                                                    className: \"btn-secondary flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_AudioWaveform_Mic_Sparkles_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                            className: \"w-5 h-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                                            lineNumber: 262,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Back\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                                            lineNumber: 263,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                                    lineNumber: 258,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: handleNext,\n                                                    disabled: !audioBlob,\n                                                    className: \"btn-primary flex items-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Create Voice Clone\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                                            lineNumber: 271,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_AudioWaveform_Mic_Sparkles_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                            className: \"w-5 h-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                                            lineNumber: 272,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                                    lineNumber: 266,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                            lineNumber: 257,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                    lineNumber: 241,\n                                    columnNumber: 15\n                                }, this),\n                                step === 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card max-w-2xl mx-auto text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"text-3xl font-bold mb-4\",\n                                                    children: [\n                                                        \"Creating Your \",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"gradient-text\",\n                                                            children: \"Voice Clone\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                                            lineNumber: 282,\n                                                            columnNumber: 35\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                                    lineNumber: 281,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-300\",\n                                                    children: \"Our AI is analyzing your voice and creating your digital twin...\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                                    lineNumber: 284,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                            lineNumber: 280,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"audio-visualizer\",\n                                                        children: [\n                                                            ...Array(16)\n                                                        ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"waveform-bar w-2 bg-gradient-to-t from-purple-600 to-blue-400 rounded-full\",\n                                                                style: {\n                                                                    animationDelay: `${i * 0.1}s`,\n                                                                    height: `${Math.random() * 40 + 10}px`\n                                                                }\n                                                            }, i, false, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                                                lineNumber: 293,\n                                                                columnNumber: 25\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                                        lineNumber: 291,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                                    lineNumber: 290,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-center space-x-3 text-gray-300\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_AudioWaveform_Mic_Sparkles_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                    className: \"w-5 h-5 text-purple-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                                                    lineNumber: 307,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Analyzing voice patterns...\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                                                    lineNumber: 308,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                                            lineNumber: 306,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-center space-x-3 text-gray-300\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_AudioWaveform_Mic_Sparkles_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                    className: \"w-5 h-5 text-blue-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                                                    lineNumber: 311,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Training AI model...\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                                                    lineNumber: 312,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                                            lineNumber: 310,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-center space-x-3 text-gray-300\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_AudioWaveform_Mic_Sparkles_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                    className: \"w-5 h-5 text-green-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                                                    lineNumber: 315,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Optimizing voice synthesis...\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                                                    lineNumber: 316,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                                            lineNumber: 314,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                                    lineNumber: 305,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: handleCreateVoiceClone,\n                                                    disabled: isProcessing,\n                                                    className: \"btn-primary w-full disabled:opacity-50 disabled:cursor-not-allowed\",\n                                                    children: isProcessing ? 'Processing...' : 'Complete Setup'\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                                    lineNumber: 320,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                            lineNumber: 289,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                    lineNumber: 279,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, step, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                            lineNumber: 176,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                    lineNumber: 140,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                lineNumber: 139,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n        lineNumber: 122,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/record/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/AudioRecorder.tsx":
/*!******************************************!*\
  !*** ./src/components/AudioRecorder.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AudioRecorder)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_Download_Mic_Pause_Play_Square_Trash2_Upload_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Mic,Pause,Play,Square,Trash2,Upload,Volume2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/mic.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Mic_Pause_Play_Square_Trash2_Upload_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Mic,Pause,Play,Square,Trash2,Upload,Volume2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/square.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Mic_Pause_Play_Square_Trash2_Upload_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Mic,Pause,Play,Square,Trash2,Upload,Volume2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Mic_Pause_Play_Square_Trash2_Upload_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Mic,Pause,Play,Square,Trash2,Upload,Volume2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/pause.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Mic_Pause_Play_Square_Trash2_Upload_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Mic,Pause,Play,Square,Trash2,Upload,Volume2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Mic_Pause_Play_Square_Trash2_Upload_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Mic,Pause,Play,Square,Trash2,Upload,Volume2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/volume-2.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Mic_Pause_Play_Square_Trash2_Upload_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Mic,Pause,Play,Square,Trash2,Upload,Volume2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Mic_Pause_Play_Square_Trash2_Upload_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Mic,Pause,Play,Square,Trash2,Upload,Volume2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _lib_audio__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/audio */ \"(ssr)/./src/lib/audio.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction AudioRecorder({ onRecordingComplete, onRecordingStart, onRecordingStop, maxDuration = 120, className = '' }) {\n    const [isRecording, setIsRecording] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isPlaying, setIsPlaying] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [audioBlob, setAudioBlob] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [audioUrl, setAudioUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [duration, setDuration] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [audioLevel, setAudioLevel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isInitialized, setIsInitialized] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [permissionStatus, setPermissionStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('unknown');\n    const recorderRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const audioRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const intervalRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const animationRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AudioRecorder.useEffect\": ()=>{\n            // Check initial permission status and auto-initialize if granted\n            const initializeIfPermitted = {\n                \"AudioRecorder.useEffect.initializeIfPermitted\": async ()=>{\n                    await checkMicrophonePermission();\n                    // If permission is already granted, auto-initialize\n                    if (permissionStatus === 'granted') {\n                        await initializeRecorder();\n                    }\n                }\n            }[\"AudioRecorder.useEffect.initializeIfPermitted\"];\n            initializeIfPermitted();\n            return ({\n                \"AudioRecorder.useEffect\": ()=>{\n                    cleanup();\n                }\n            })[\"AudioRecorder.useEffect\"];\n        }\n    }[\"AudioRecorder.useEffect\"], []);\n    // Auto-initialize when permission status changes to granted\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AudioRecorder.useEffect\": ()=>{\n            if (permissionStatus === 'granted' && !isInitialized) {\n                initializeRecorder();\n            }\n        }\n    }[\"AudioRecorder.useEffect\"], [\n        permissionStatus,\n        isInitialized\n    ]);\n    const checkMicrophonePermission = async ()=>{\n        try {\n            if (navigator.permissions) {\n                const permission = await navigator.permissions.query({\n                    name: 'microphone'\n                });\n                setPermissionStatus(permission.state);\n                permission.onchange = ()=>{\n                    setPermissionStatus(permission.state);\n                };\n            } else {\n                // Fallback: try to access microphone to check permission\n                try {\n                    const stream = await navigator.mediaDevices.getUserMedia({\n                        audio: true\n                    });\n                    stream.getTracks().forEach((track)=>track.stop());\n                    setPermissionStatus('granted');\n                } catch (err) {\n                    setPermissionStatus('denied');\n                }\n            }\n        } catch (err) {\n            console.log('Permission check failed:', err);\n            setPermissionStatus('unknown');\n        }\n    };\n    const requestMicrophonePermission = async ()=>{\n        try {\n            setError(null);\n            console.log('Requesting microphone permission...');\n            const stream = await navigator.mediaDevices.getUserMedia({\n                audio: {\n                    sampleRate: 44100,\n                    channelCount: 1,\n                    echoCancellation: true,\n                    noiseSuppression: true,\n                    autoGainControl: true\n                }\n            });\n            // Stop the test stream immediately\n            stream.getTracks().forEach((track)=>track.stop());\n            setPermissionStatus('granted');\n            setError(null);\n            // Auto-initialize after permission granted\n            await initializeRecorder();\n        } catch (err) {\n            console.error('Permission request failed:', err);\n            setPermissionStatus('denied');\n            if (err instanceof Error) {\n                if (err.name === 'NotAllowedError') {\n                    setError('Microphone access denied. Please click the microphone icon in your browser\\'s address bar and allow access.');\n                } else if (err.name === 'NotFoundError') {\n                    setError('No microphone found. Please connect a microphone and try again.');\n                } else {\n                    setError(`Microphone error: ${err.message}`);\n                }\n            }\n        }\n    };\n    const cleanup = ()=>{\n        if (intervalRef.current) {\n            clearInterval(intervalRef.current);\n        }\n        if (animationRef.current) {\n            cancelAnimationFrame(animationRef.current);\n        }\n        if (recorderRef.current) {\n            recorderRef.current.cleanup();\n        }\n        if (audioUrl) {\n            URL.revokeObjectURL(audioUrl);\n        }\n    };\n    const initializeRecorder = async ()=>{\n        try {\n            setError(null);\n            // Check if browser supports required APIs\n            if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {\n                throw new Error('Your browser does not support audio recording');\n            }\n            // Request microphone permission explicitly\n            console.log('Requesting microphone permission...');\n            const stream = await navigator.mediaDevices.getUserMedia({\n                audio: {\n                    sampleRate: 44100,\n                    channelCount: 1,\n                    echoCancellation: true,\n                    noiseSuppression: true,\n                    autoGainControl: true\n                }\n            });\n            // Stop the test stream\n            stream.getTracks().forEach((track)=>track.stop());\n            // Now initialize the recorder\n            recorderRef.current = new _lib_audio__WEBPACK_IMPORTED_MODULE_2__.AudioRecorder();\n            await recorderRef.current.initialize();\n            setIsInitialized(true);\n            console.log('Audio recorder initialized successfully');\n        } catch (err) {\n            console.error('Recorder initialization failed:', err);\n            if (err instanceof Error) {\n                if (err.name === 'NotAllowedError') {\n                    setError('Microphone access denied. Please allow microphone permissions and refresh the page.');\n                } else if (err.name === 'NotFoundError') {\n                    setError('No microphone found. Please connect a microphone and try again.');\n                } else if (err.name === 'NotSupportedError') {\n                    setError('Your browser does not support audio recording.');\n                } else {\n                    setError(`Microphone error: ${err.message}`);\n                }\n            } else {\n                setError('Failed to access microphone. Please check permissions.');\n            }\n        }\n    };\n    const startRecording = async ()=>{\n        try {\n            setError(null);\n            if (!recorderRef.current || !isInitialized) {\n                console.log('Initializing recorder...');\n                await initializeRecorder();\n            }\n            if (!recorderRef.current || !isInitialized) {\n                setError('Failed to initialize microphone. Please check permissions and try again.');\n                return;\n            }\n            console.log('Starting recording...');\n            setDuration(0);\n            recorderRef.current.startRecording();\n            setIsRecording(true);\n            onRecordingStart?.();\n            // Start duration timer\n            intervalRef.current = setInterval(()=>{\n                setDuration((prev)=>{\n                    const newDuration = prev + 1;\n                    if (newDuration >= maxDuration) {\n                        stopRecording();\n                    }\n                    return newDuration;\n                });\n            }, 1000);\n            // Start audio level monitoring\n            const updateAudioLevel = ()=>{\n                if (recorderRef.current && isRecording) {\n                    setAudioLevel(recorderRef.current.getAudioLevel());\n                    animationRef.current = requestAnimationFrame(updateAudioLevel);\n                }\n            };\n            updateAudioLevel();\n        } catch (err) {\n            console.error('Recording start failed:', err);\n            setError('Failed to start recording. Please check microphone permissions.');\n        }\n    };\n    const stopRecording = async ()=>{\n        if (!recorderRef.current || !isRecording) return;\n        try {\n            const blob = await recorderRef.current.stopRecording();\n            setIsRecording(false);\n            setAudioLevel(0);\n            onRecordingStop?.();\n            if (intervalRef.current) {\n                clearInterval(intervalRef.current);\n            }\n            if (animationRef.current) {\n                cancelAnimationFrame(animationRef.current);\n            }\n            // Clean up previous audio URL\n            if (audioUrl) {\n                URL.revokeObjectURL(audioUrl);\n            }\n            const newAudioUrl = _lib_audio__WEBPACK_IMPORTED_MODULE_2__.audioUtils.createAudioURL(blob);\n            setAudioBlob(blob);\n            setAudioUrl(newAudioUrl);\n            onRecordingComplete?.(blob);\n        } catch (err) {\n            setError('Failed to stop recording');\n            console.error('Recording stop failed:', err);\n        }\n    };\n    const playAudio = ()=>{\n        if (!audioRef.current || !audioUrl) return;\n        if (isPlaying) {\n            audioRef.current.pause();\n            setIsPlaying(false);\n        } else {\n            audioRef.current.play();\n            setIsPlaying(true);\n        }\n    };\n    const downloadAudio = ()=>{\n        if (!audioBlob) return;\n        _lib_audio__WEBPACK_IMPORTED_MODULE_2__.audioUtils.downloadAudio(audioBlob, `voice-recording-${Date.now()}.webm`);\n    };\n    const deleteRecording = ()=>{\n        if (audioUrl) {\n            URL.revokeObjectURL(audioUrl);\n        }\n        setAudioBlob(null);\n        setAudioUrl(null);\n        setDuration(0);\n        setIsPlaying(false);\n    };\n    const handleFileUpload = (event)=>{\n        const file = event.target.files?.[0];\n        if (!file) return;\n        const validation = _lib_audio__WEBPACK_IMPORTED_MODULE_2__.audioUtils.validateAudioFile(file);\n        if (!validation.valid) {\n            setError(validation.error || 'Invalid audio file');\n            return;\n        }\n        setError(null);\n        const blob = new Blob([\n            file\n        ], {\n            type: file.type\n        });\n        const url = _lib_audio__WEBPACK_IMPORTED_MODULE_2__.audioUtils.createAudioURL(blob);\n        if (audioUrl) {\n            URL.revokeObjectURL(audioUrl);\n        }\n        setAudioBlob(blob);\n        setAudioUrl(url);\n        onRecordingComplete?.(blob);\n    };\n    const formatTime = (seconds)=>{\n        const mins = Math.floor(seconds / 60);\n        const secs = seconds % 60;\n        return `${mins}:${secs.toString().padStart(2, '0')}`;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `space-y-6 ${className}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.AnimatePresence, {\n                children: error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: -10\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    exit: {\n                        opacity: 0,\n                        y: -10\n                    },\n                    className: \"bg-red-900/50 border border-red-700 rounded-lg p-4 text-red-200\",\n                    children: error\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                    lineNumber: 327,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                lineNumber: 325,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"card\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold mb-2\",\n                                children: \"Record Your Voice\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                lineNumber: 341,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400 mb-2\",\n                                children: [\n                                    \"Record at least 30 seconds for best results (max \",\n                                    maxDuration,\n                                    \"s)\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                lineNumber: 342,\n                                columnNumber: 11\n                            }, this),\n                            permissionStatus === 'unknown' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-blue-300\",\n                                children: \"\\uD83D\\uDCA1 On macOS: Make sure to allow microphone access in System Preferences → Security & Privacy → Microphone\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                lineNumber: 346,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                        lineNumber: 340,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-center mb-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"audio-visualizer\",\n                            children: [\n                                ...Array(12)\n                            ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"audio-bar w-3\",\n                                    style: {\n                                        height: isRecording ? `${Math.max(4, audioLevel * 60 + Math.random() * 20)}px` : '4px'\n                                    }\n                                }, i, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                    lineNumber: 356,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                            lineNumber: 354,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                        lineNumber: 353,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-2xl font-mono text-purple-400\",\n                                children: formatTime(duration)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                lineNumber: 371,\n                                columnNumber: 11\n                            }, this),\n                            maxDuration && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-gray-400\",\n                                children: [\n                                    \"/ \",\n                                    formatTime(maxDuration)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                lineNumber: 375,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                        lineNumber: 370,\n                        columnNumber: 9\n                    }, this),\n                    permissionStatus === 'denied' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6 p-4 bg-yellow-900/50 border border-yellow-700 rounded-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2 text-yellow-200 mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Mic_Pause_Play_Square_Trash2_Upload_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                        lineNumber: 385,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: \"Microphone Permission Required\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                        lineNumber: 386,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                lineNumber: 384,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-yellow-300 mb-3\",\n                                children: \"Please allow microphone access to record your voice. Click the button below to request permission.\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                lineNumber: 388,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: requestMicrophonePermission,\n                                className: \"btn-primary flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Mic_Pause_Play_Square_Trash2_Upload_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                        lineNumber: 395,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Allow Microphone Access\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                        lineNumber: 396,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                lineNumber: 391,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                        lineNumber: 383,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-center space-x-4 mb-6\",\n                        children: [\n                            (permissionStatus === 'unknown' || !isInitialized) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: requestMicrophonePermission,\n                                className: \"btn-primary flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Mic_Pause_Play_Square_Trash2_Upload_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                        lineNumber: 408,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Enable Microphone\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                        lineNumber: 409,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                lineNumber: 404,\n                                columnNumber: 13\n                            }, this),\n                            permissionStatus === 'granted' && isInitialized && !isRecording && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: startRecording,\n                                className: \"btn-primary flex items-center space-x-2\",\n                                disabled: isRecording,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Mic_Pause_Play_Square_Trash2_Upload_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                        lineNumber: 419,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Start Recording\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                        lineNumber: 420,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                lineNumber: 414,\n                                columnNumber: 13\n                            }, this),\n                            isRecording && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: stopRecording,\n                                className: \"bg-red-600 hover:bg-red-700 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-200 flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Mic_Pause_Play_Square_Trash2_Upload_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                        lineNumber: 429,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Stop Recording\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                        lineNumber: 430,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                lineNumber: 425,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"btn-secondary cursor-pointer flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Mic_Pause_Play_Square_Trash2_Upload_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                        lineNumber: 436,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Upload Audio\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                        lineNumber: 437,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"file\",\n                                        accept: \"audio/*\",\n                                        onChange: handleFileUpload,\n                                        className: \"hidden\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                        lineNumber: 438,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                lineNumber: 435,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                        lineNumber: 402,\n                        columnNumber: 9\n                    }, this),\n                    isRecording && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full bg-gray-700 rounded-full h-2 mb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gradient-to-r from-purple-600 to-blue-600 h-2 rounded-full transition-all duration-1000\",\n                            style: {\n                                width: `${duration / maxDuration * 100}%`\n                            }\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                            lineNumber: 450,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                        lineNumber: 449,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                lineNumber: 339,\n                columnNumber: 7\n            }, this),\n            audioUrl && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                initial: {\n                    opacity: 0,\n                    y: 20\n                },\n                animate: {\n                    opacity: 1,\n                    y: 0\n                },\n                className: \"card\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: playAudio,\n                                        className: \"btn-secondary flex items-center space-x-2\",\n                                        children: [\n                                            isPlaying ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Mic_Pause_Play_Square_Trash2_Upload_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                                lineNumber: 472,\n                                                columnNumber: 19\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Mic_Pause_Play_Square_Trash2_Upload_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                                lineNumber: 474,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: isPlaying ? 'Pause' : 'Play'\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                                lineNumber: 476,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                        lineNumber: 467,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2 text-gray-400\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Mic_Pause_Play_Square_Trash2_Upload_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                                lineNumber: 480,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Recording ready\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                                lineNumber: 481,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                        lineNumber: 479,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                lineNumber: 466,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: downloadAudio,\n                                        className: \"btn-secondary flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Mic_Pause_Play_Square_Trash2_Upload_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                                lineNumber: 490,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Download\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                                lineNumber: 491,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                        lineNumber: 486,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: deleteRecording,\n                                        className: \"text-red-400 hover:text-red-300 p-2 rounded-lg hover:bg-red-900/20 transition-colors\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Mic_Pause_Play_Square_Trash2_Upload_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                            lineNumber: 498,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                        lineNumber: 494,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                lineNumber: 485,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                        lineNumber: 465,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"audio\", {\n                        ref: audioRef,\n                        src: audioUrl,\n                        onEnded: ()=>setIsPlaying(false),\n                        onPlay: ()=>setIsPlaying(true),\n                        onPause: ()=>setIsPlaying(false),\n                        className: \"hidden\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                        lineNumber: 503,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                lineNumber: 460,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n        lineNumber: 323,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/AudioRecorder.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/audio.ts":
/*!**************************!*\
  !*** ./src/lib/audio.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AudioRecorder: () => (/* binding */ AudioRecorder),\n/* harmony export */   audioUtils: () => (/* binding */ audioUtils)\n/* harmony export */ });\n// Audio recording and processing utilities\nclass AudioRecorder {\n    constructor(options = {}){\n        this.options = options;\n        this.mediaRecorder = null;\n        this.audioChunks = [];\n        this.stream = null;\n        this.audioContext = null;\n        this.analyser = null;\n        this.dataArray = null;\n        this.options = {\n            mimeType: 'audio/webm;codecs=opus',\n            audioBitsPerSecond: 128000,\n            sampleRate: 44100,\n            ...options\n        };\n    }\n    async initialize() {\n        try {\n            console.log('AudioRecorder: Starting initialization...');\n            // Check browser support\n            if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {\n                throw new Error('Browser does not support audio recording');\n            }\n            console.log('AudioRecorder: Requesting media stream...');\n            this.stream = await navigator.mediaDevices.getUserMedia({\n                audio: {\n                    sampleRate: this.options.sampleRate,\n                    channelCount: 1,\n                    echoCancellation: true,\n                    noiseSuppression: true,\n                    autoGainControl: true\n                }\n            });\n            console.log('AudioRecorder: Media stream obtained, setting up audio context...');\n            // Set up audio analysis\n            this.audioContext = new (window.AudioContext || window.webkitAudioContext)();\n            // Resume audio context if it's suspended (required by some browsers)\n            if (this.audioContext.state === 'suspended') {\n                await this.audioContext.resume();\n            }\n            this.analyser = this.audioContext.createAnalyser();\n            this.analyser.fftSize = 256;\n            const source = this.audioContext.createMediaStreamSource(this.stream);\n            source.connect(this.analyser);\n            this.dataArray = new Uint8Array(this.analyser.frequencyBinCount);\n            console.log('AudioRecorder: Setting up MediaRecorder...');\n            // Check MediaRecorder support and find supported mime type\n            let mimeType = this.options.mimeType;\n            if (!MediaRecorder.isTypeSupported(mimeType)) {\n                const supportedTypes = [\n                    'audio/webm;codecs=opus',\n                    'audio/webm',\n                    'audio/mp4',\n                    'audio/wav'\n                ];\n                mimeType = supportedTypes.find((type)=>MediaRecorder.isTypeSupported(type));\n                if (!mimeType) {\n                    throw new Error('No supported audio format found');\n                }\n                console.log(`AudioRecorder: Using fallback mime type: ${mimeType}`);\n            }\n            // Set up MediaRecorder\n            this.mediaRecorder = new MediaRecorder(this.stream, {\n                mimeType,\n                audioBitsPerSecond: this.options.audioBitsPerSecond\n            });\n            this.mediaRecorder.ondataavailable = (event)=>{\n                if (event.data.size > 0) {\n                    this.audioChunks.push(event.data);\n                }\n            };\n            console.log('AudioRecorder: Initialization complete!');\n        } catch (error) {\n            console.error('AudioRecorder initialization failed:', error);\n            this.cleanup();\n            throw new Error(`Failed to initialize audio recorder: ${error}`);\n        }\n    }\n    startRecording() {\n        if (!this.mediaRecorder) {\n            throw new Error('Audio recorder not initialized');\n        }\n        this.audioChunks = [];\n        this.mediaRecorder.start(100) // Collect data every 100ms\n        ;\n    }\n    stopRecording() {\n        return new Promise((resolve, reject)=>{\n            if (!this.mediaRecorder) {\n                reject(new Error('Audio recorder not initialized'));\n                return;\n            }\n            this.mediaRecorder.onstop = ()=>{\n                const audioBlob = new Blob(this.audioChunks, {\n                    type: this.options.mimeType\n                });\n                resolve(audioBlob);\n            };\n            this.mediaRecorder.stop();\n        });\n    }\n    getAudioLevel() {\n        if (!this.analyser || !this.dataArray) return 0;\n        this.analyser.getByteFrequencyData(this.dataArray);\n        const average = this.dataArray.reduce((sum, value)=>sum + value, 0) / this.dataArray.length;\n        return average / 255 // Normalize to 0-1\n        ;\n    }\n    isRecording() {\n        return this.mediaRecorder?.state === 'recording';\n    }\n    cleanup() {\n        if (this.mediaRecorder && this.mediaRecorder.state !== 'inactive') {\n            this.mediaRecorder.stop();\n        }\n        if (this.stream) {\n            this.stream.getTracks().forEach((track)=>track.stop());\n            this.stream = null;\n        }\n        if (this.audioContext && this.audioContext.state !== 'closed') {\n            this.audioContext.close();\n            this.audioContext = null;\n        }\n        this.mediaRecorder = null;\n        this.analyser = null;\n        this.dataArray = null;\n        this.audioChunks = [];\n    }\n}\n// Audio utility functions\nconst audioUtils = {\n    // Convert blob to base64\n    blobToBase64 (blob) {\n        return new Promise((resolve, reject)=>{\n            const reader = new FileReader();\n            reader.onload = ()=>{\n                const result = reader.result;\n                resolve(result.split(',')[1]) // Remove data:audio/webm;base64, prefix\n                ;\n            };\n            reader.onerror = reject;\n            reader.readAsDataURL(blob);\n        });\n    },\n    // Convert blob to array buffer\n    blobToArrayBuffer (blob) {\n        return new Promise((resolve, reject)=>{\n            const reader = new FileReader();\n            reader.onload = ()=>resolve(reader.result);\n            reader.onerror = reject;\n            reader.readAsArrayBuffer(blob);\n        });\n    },\n    // Create audio URL from blob\n    createAudioURL (blob) {\n        return URL.createObjectURL(blob);\n    },\n    // Download audio file\n    downloadAudio (blob, filename) {\n        const url = this.createAudioURL(blob);\n        const a = document.createElement('a');\n        a.href = url;\n        a.download = filename;\n        document.body.appendChild(a);\n        a.click();\n        document.body.removeChild(a);\n        URL.revokeObjectURL(url);\n    },\n    // Format duration in seconds to MM:SS\n    formatDuration (seconds) {\n        const minutes = Math.floor(seconds / 60);\n        const remainingSeconds = Math.floor(seconds % 60);\n        return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;\n    },\n    // Validate audio file\n    validateAudioFile (file) {\n        const maxSize = 10 * 1024 * 1024 // 10MB\n        ;\n        const allowedTypes = [\n            'audio/wav',\n            'audio/mp3',\n            'audio/mpeg',\n            'audio/webm',\n            'audio/ogg'\n        ];\n        if (file.size > maxSize) {\n            return {\n                valid: false,\n                error: 'File size must be less than 10MB'\n            };\n        }\n        if (!allowedTypes.includes(file.type)) {\n            return {\n                valid: false,\n                error: 'File must be a valid audio format (WAV, MP3, WebM, OGG)'\n            };\n        }\n        return {\n            valid: true\n        };\n    },\n    // Convert array buffer to blob\n    arrayBufferToBlob (buffer, mimeType = 'audio/mpeg') {\n        return new Blob([\n            buffer\n        ], {\n            type: mimeType\n        });\n    },\n    // Get audio duration\n    getAudioDuration (file) {\n        return new Promise((resolve, reject)=>{\n            const audio = new Audio();\n            audio.onloadedmetadata = ()=>{\n                resolve(audio.duration);\n            };\n            audio.onerror = reject;\n            audio.src = URL.createObjectURL(file);\n        });\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/audio.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/localStorage.ts":
/*!*********************************!*\
  !*** ./src/lib/localStorage.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   localStorageService: () => (/* binding */ localStorageService)\n/* harmony export */ });\n// Local storage utilities for voice models\n// This serves as a fallback when Supabase is not available\nconst VOICE_MODELS_KEY = 'voice_clone_models';\nconst localStorageService = {\n    // Get all voice models from localStorage\n    getVoiceModels () {\n        if (true) return [];\n        try {\n            const stored = localStorage.getItem(VOICE_MODELS_KEY);\n            return stored ? JSON.parse(stored) : [];\n        } catch (error) {\n            console.error('Error reading voice models from localStorage:', error);\n            return [];\n        }\n    },\n    // Save a voice model to localStorage\n    saveVoiceModel (voiceModel) {\n        if (true) return;\n        try {\n            const existing = this.getVoiceModels();\n            const updated = [\n                ...existing,\n                voiceModel\n            ];\n            localStorage.setItem(VOICE_MODELS_KEY, JSON.stringify(updated));\n        } catch (error) {\n            console.error('Error saving voice model to localStorage:', error);\n        }\n    },\n    // Remove a voice model from localStorage\n    removeVoiceModel (voiceId) {\n        if (true) return;\n        try {\n            const existing = this.getVoiceModels();\n            const filtered = existing.filter((model)=>model.voice_id !== voiceId);\n            localStorage.setItem(VOICE_MODELS_KEY, JSON.stringify(filtered));\n        } catch (error) {\n            console.error('Error removing voice model from localStorage:', error);\n        }\n    },\n    // Clear all voice models\n    clearVoiceModels () {\n        if (true) return;\n        try {\n            localStorage.removeItem(VOICE_MODELS_KEY);\n        } catch (error) {\n            console.error('Error clearing voice models from localStorage:', error);\n        }\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/localStorage.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/framer-motion","vendor-chunks/motion-dom","vendor-chunks/lucide-react","vendor-chunks/motion-utils"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Frecord%2Fpage&page=%2Frecord%2Fpage&appPaths=%2Frecord%2Fpage&pagePath=private-next-app-dir%2Frecord%2Fpage.tsx&appDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();