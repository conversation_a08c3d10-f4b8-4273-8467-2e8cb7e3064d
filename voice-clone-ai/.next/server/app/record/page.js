/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/record/page";
exports.ids = ["app/record/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Frecord%2Fpage&page=%2Frecord%2Fpage&appPaths=%2Frecord%2Fpage&pagePath=private-next-app-dir%2Frecord%2Fpage.tsx&appDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Frecord%2Fpage&page=%2Frecord%2Fpage&appPaths=%2Frecord%2Fpage&pagePath=private-next-app-dir%2Frecord%2Fpage.tsx&appDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/record/page.tsx */ \"(rsc)/./src/app/record/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'record',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/record/page\",\n        pathname: \"/record\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Frecord%2Fpage&page=%2Frecord%2Fpage&appPaths=%2Frecord%2Fpage&pagePath=private-next-app-dir%2Frecord%2Fpage.tsx&appDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fsrc%2Fapp%2Frecord%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fsrc%2Fapp%2Frecord%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/record/page.tsx */ \"(rsc)/./src/app/record/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGdGFoYWZhcm9vcXVpJTJGRG9jdW1lbnRzJTJGYXVnbWVudC1wcm9qZWN0cyUyRkFJLUFnZW50JTJGdm9pY2UtY2xvbmUtYWklMkZzcmMlMkZhcHAlMkZyZWNvcmQlMkZwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsOEpBQW1JIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvdGFoYWZhcm9vcXVpL0RvY3VtZW50cy9hdWdtZW50LXByb2plY3RzL0FJLUFnZW50L3ZvaWNlLWNsb25lLWFpL3NyYy9hcHAvcmVjb3JkL3BhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fsrc%2Fapp%2Frecord%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIi9Vc2Vycy90YWhhZmFyb29xdWkvRG9jdW1lbnRzL2F1Z21lbnQtcHJvamVjdHMvQUktQWdlbnQvdm9pY2UtY2xvbmUtYWkvc3JjL2FwcC9mYXZpY29uLmljbz9fX25leHRfbWV0YWRhdGFfXyJdLCJzb3VyY2VzQ29udGVudCI6WyIgIGltcG9ydCB7IGZpbGxNZXRhZGF0YVNlZ21lbnQgfSBmcm9tICduZXh0L2Rpc3QvbGliL21ldGFkYXRhL2dldC1tZXRhZGF0YS1yb3V0ZSdcblxuICBleHBvcnQgZGVmYXVsdCBhc3luYyAocHJvcHMpID0+IHtcbiAgICBjb25zdCBpbWFnZURhdGEgPSB7XCJ0eXBlXCI6XCJpbWFnZS94LWljb25cIixcInNpemVzXCI6XCIxNngxNlwifVxuICAgIGNvbnN0IGltYWdlVXJsID0gZmlsbE1ldGFkYXRhU2VnbWVudChcIi5cIiwgYXdhaXQgcHJvcHMucGFyYW1zLCBcImZhdmljb24uaWNvXCIpXG5cbiAgICByZXR1cm4gW3tcbiAgICAgIC4uLmltYWdlRGF0YSxcbiAgICAgIHVybDogaW1hZ2VVcmwgKyBcIlwiLFxuICAgIH1dXG4gIH0iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"11f160e9a53d\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyIvVXNlcnMvdGFoYWZhcm9vcXVpL0RvY3VtZW50cy9hdWdtZW50LXByb2plY3RzL0FJLUFnZW50L3ZvaWNlLWNsb25lLWFpL3NyYy9hcHAvZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCIxMWYxNjBlOWE1M2RcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\n\nconst metadata = {\n    title: \"VoiceClone AI - Clone Your Voice in Seconds\",\n    description: \"Create realistic voice clones using advanced AI technology. Generate speech in your own voice from any text.\",\n    keywords: \"voice cloning, AI voice, text to speech, voice synthesis, artificial intelligence\",\n    authors: [\n        {\n            name: \"VoiceClone AI\"\n        }\n    ],\n    openGraph: {\n        title: \"VoiceClone AI - Clone Your Voice in Seconds\",\n        description: \"Create realistic voice clones using advanced AI technology\",\n        type: \"website\",\n        url: \"https://voiceclone-ai.vercel.app\"\n    },\n    twitter: {\n        card: \"summary_large_image\",\n        title: \"VoiceClone AI - Clone Your Voice in Seconds\",\n        description: \"Create realistic voice clones using advanced AI technology\"\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        className: \"dark\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default().className)} antialiased bg-black text-white min-h-screen`,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative min-h-screen\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed inset-0 bg-gradient-to-br from-purple-900/20 via-black to-blue-900/20 pointer-events-none\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/layout.tsx\",\n                        lineNumber: 35,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative z-10\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/layout.tsx\",\n                        lineNumber: 38,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/layout.tsx\",\n                lineNumber: 33,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/layout.tsx\",\n            lineNumber: 32,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/layout.tsx\",\n        lineNumber: 31,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/record/page.tsx":
/*!*********************************!*\
  !*** ./src/app/record/page.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx",
"default",
));


/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fsrc%2Fapp%2Frecord%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fsrc%2Fapp%2Frecord%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/record/page.tsx */ \"(ssr)/./src/app/record/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGdGFoYWZhcm9vcXVpJTJGRG9jdW1lbnRzJTJGYXVnbWVudC1wcm9qZWN0cyUyRkFJLUFnZW50JTJGdm9pY2UtY2xvbmUtYWklMkZzcmMlMkZhcHAlMkZyZWNvcmQlMkZwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsOEpBQW1JIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvdGFoYWZhcm9vcXVpL0RvY3VtZW50cy9hdWdtZW50LXByb2plY3RzL0FJLUFnZW50L3ZvaWNlLWNsb25lLWFpL3NyYy9hcHAvcmVjb3JkL3BhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fsrc%2Fapp%2Frecord%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/record/page.tsx":
/*!*********************************!*\
  !*** ./src/app/record/page.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RecordPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_AudioWaveform_Mic_Sparkles_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,AudioWaveform,Mic,Sparkles,Volume2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_AudioWaveform_Mic_Sparkles_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,AudioWaveform,Mic,Sparkles,Volume2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_AudioWaveform_Mic_Sparkles_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,AudioWaveform,Mic,Sparkles,Volume2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_AudioWaveform_Mic_Sparkles_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,AudioWaveform,Mic,Sparkles,Volume2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/mic.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_AudioWaveform_Mic_Sparkles_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,AudioWaveform,Mic,Sparkles,Volume2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/audio-waveform.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_AudioWaveform_Mic_Sparkles_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,AudioWaveform,Mic,Sparkles,Volume2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/volume-2.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_AudioRecorder__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/AudioRecorder */ \"(ssr)/./src/components/AudioRecorder.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nfunction RecordPage() {\n    const [step, setStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [voiceName, setVoiceName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [voiceDescription, setVoiceDescription] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [audioBlob, setAudioBlob] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isProcessing, setIsProcessing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleRecordingComplete = (blob)=>{\n        setAudioBlob(blob);\n    };\n    const handleNext = ()=>{\n        if (step === 1 && voiceName.trim()) {\n            setStep(2);\n        } else if (step === 2 && audioBlob) {\n            setStep(3);\n        }\n    };\n    const handleCreateVoiceClone = async ()=>{\n        if (!audioBlob || !voiceName.trim()) return;\n        setIsProcessing(true);\n        try {\n            // TODO: Implement voice cloning API call\n            console.log('Creating voice clone...', {\n                voiceName,\n                voiceDescription,\n                audioBlob\n            });\n            // Simulate processing time\n            await new Promise((resolve)=>setTimeout(resolve, 3000));\n            // Redirect to generation page\n            window.location.href = '/generate';\n        } catch (error) {\n            console.error('Voice cloning failed:', error);\n        } finally{\n            setIsProcessing(false);\n        }\n    };\n    const steps = [\n        {\n            number: 1,\n            title: 'Voice Details',\n            description: 'Name your voice clone'\n        },\n        {\n            number: 2,\n            title: 'Record Audio',\n            description: 'Capture your voice'\n        },\n        {\n            number: 3,\n            title: 'Create Clone',\n            description: 'Generate your AI voice'\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen py-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"fixed top-0 w-full z-50 bg-black/80 backdrop-blur-md border-b border-gray-800\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center h-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/\",\n                                className: \"flex items-center space-x-2 text-gray-300 hover:text-white transition-colors\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_AudioWaveform_Mic_Sparkles_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                        lineNumber: 61,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Back to Home\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                        lineNumber: 62,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                lineNumber: 60,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_AudioWaveform_Mic_Sparkles_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"w-8 h-8 text-purple-400\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                        lineNumber: 65,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xl font-bold gradient-text\",\n                                        children: \"VoiceClone AI\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                        lineNumber: 66,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                lineNumber: 64,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                        lineNumber: 59,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                    lineNumber: 58,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                lineNumber: 57,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"pt-24 px-4 sm:px-6 lg:px-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-4xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-12\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center space-x-8\",\n                                children: steps.map((stepItem, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: `w-12 h-12 rounded-full flex items-center justify-center font-semibold transition-all duration-300 ${step >= stepItem.number ? 'bg-gradient-to-r from-purple-600 to-blue-600 text-white' : 'bg-gray-800 text-gray-400'}`,\n                                                        children: stepItem.number\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                                        lineNumber: 80,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mt-2 text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm font-medium\",\n                                                                children: stepItem.title\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                                                lineNumber: 90,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-gray-400\",\n                                                                children: stepItem.description\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                                                lineNumber: 91,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                                        lineNumber: 89,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                                lineNumber: 79,\n                                                columnNumber: 19\n                                            }, this),\n                                            index < steps.length - 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: `w-16 h-1 mx-4 transition-all duration-300 ${step > stepItem.number ? 'bg-gradient-to-r from-purple-600 to-blue-600' : 'bg-gray-800'}`\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                                lineNumber: 95,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, stepItem.number, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                        lineNumber: 78,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                lineNumber: 76,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                            lineNumber: 75,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                x: 20\n                            },\n                            animate: {\n                                opacity: 1,\n                                x: 0\n                            },\n                            exit: {\n                                opacity: 0,\n                                x: -20\n                            },\n                            transition: {\n                                duration: 0.3\n                            },\n                            children: [\n                                step === 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card max-w-2xl mx-auto\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center mb-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"text-3xl font-bold mb-4\",\n                                                    children: [\n                                                        \"Create Your \",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"gradient-text\",\n                                                            children: \"Voice Clone\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                                            lineNumber: 120,\n                                                            columnNumber: 33\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                                    lineNumber: 119,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-300\",\n                                                    children: \"Let's start by giving your voice clone a name and description\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                                    lineNumber: 122,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                            lineNumber: 118,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium mb-2\",\n                                                            children: \"Voice Name *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                                            lineNumber: 129,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            value: voiceName,\n                                                            onChange: (e)=>setVoiceName(e.target.value),\n                                                            placeholder: \"e.g., My Professional Voice\",\n                                                            className: \"input-field w-full\",\n                                                            maxLength: 50\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                                            lineNumber: 132,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-gray-400 mt-1\",\n                                                            children: [\n                                                                voiceName.length,\n                                                                \"/50 characters\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                                            lineNumber: 140,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                                    lineNumber: 128,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium mb-2\",\n                                                            children: \"Description (Optional)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                                            lineNumber: 146,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                            value: voiceDescription,\n                                                            onChange: (e)=>setVoiceDescription(e.target.value),\n                                                            placeholder: \"Describe the tone, style, or intended use of this voice...\",\n                                                            className: \"input-field w-full h-24 resize-none\",\n                                                            maxLength: 200\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                                            lineNumber: 149,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-gray-400 mt-1\",\n                                                            children: [\n                                                                voiceDescription.length,\n                                                                \"/200 characters\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                                            lineNumber: 156,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                                    lineNumber: 145,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: handleNext,\n                                                    disabled: !voiceName.trim(),\n                                                    className: \"btn-primary w-full flex items-center justify-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Continue to Recording\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                                            lineNumber: 166,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_AudioWaveform_Mic_Sparkles_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                            className: \"w-5 h-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                                            lineNumber: 167,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                                    lineNumber: 161,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                            lineNumber: 127,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                    lineNumber: 117,\n                                    columnNumber: 15\n                                }, this),\n                                step === 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"max-w-3xl mx-auto\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center mb-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"text-3xl font-bold mb-4\",\n                                                    children: [\n                                                        \"Record Your \",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"gradient-text\",\n                                                            children: \"Voice Sample\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                                            lineNumber: 177,\n                                                            columnNumber: 33\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                                    lineNumber: 176,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-300 max-w-2xl mx-auto\",\n                                                    children: \"Record at least 30 seconds of clear speech. Read naturally and avoid background noise for the best results.\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                                    lineNumber: 179,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                            lineNumber: 175,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AudioRecorder__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            onRecordingComplete: handleRecordingComplete,\n                                            maxDuration: 120,\n                                            className: \"mb-8\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                            lineNumber: 184,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setStep(1),\n                                                    className: \"btn-secondary flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_AudioWaveform_Mic_Sparkles_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            className: \"w-5 h-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                                            lineNumber: 195,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Back\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                                            lineNumber: 196,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                                    lineNumber: 191,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: handleNext,\n                                                    disabled: !audioBlob,\n                                                    className: \"btn-primary flex items-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Create Voice Clone\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                                            lineNumber: 204,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_AudioWaveform_Mic_Sparkles_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                            className: \"w-5 h-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                                            lineNumber: 205,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                                    lineNumber: 199,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                            lineNumber: 190,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                    lineNumber: 174,\n                                    columnNumber: 15\n                                }, this),\n                                step === 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card max-w-2xl mx-auto text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"text-3xl font-bold mb-4\",\n                                                    children: [\n                                                        \"Creating Your \",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"gradient-text\",\n                                                            children: \"Voice Clone\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                                            lineNumber: 215,\n                                                            columnNumber: 35\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                                    lineNumber: 214,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-300\",\n                                                    children: \"Our AI is analyzing your voice and creating your digital twin...\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                                    lineNumber: 217,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                            lineNumber: 213,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"audio-visualizer\",\n                                                        children: [\n                                                            ...Array(16)\n                                                        ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"waveform-bar w-2 bg-gradient-to-t from-purple-600 to-blue-400 rounded-full\",\n                                                                style: {\n                                                                    animationDelay: `${i * 0.1}s`,\n                                                                    height: `${Math.random() * 40 + 10}px`\n                                                                }\n                                                            }, i, false, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                                                lineNumber: 226,\n                                                                columnNumber: 25\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                                        lineNumber: 224,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                                    lineNumber: 223,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-center space-x-3 text-gray-300\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_AudioWaveform_Mic_Sparkles_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                    className: \"w-5 h-5 text-purple-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                                                    lineNumber: 240,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Analyzing voice patterns...\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                                                    lineNumber: 241,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                                            lineNumber: 239,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-center space-x-3 text-gray-300\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_AudioWaveform_Mic_Sparkles_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                    className: \"w-5 h-5 text-blue-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                                                    lineNumber: 244,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Training AI model...\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                                                    lineNumber: 245,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                                            lineNumber: 243,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-center space-x-3 text-gray-300\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_AudioWaveform_Mic_Sparkles_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                    className: \"w-5 h-5 text-green-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                                                    lineNumber: 248,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Optimizing voice synthesis...\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                                                    lineNumber: 249,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                                            lineNumber: 247,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                                    lineNumber: 238,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: handleCreateVoiceClone,\n                                                    disabled: isProcessing,\n                                                    className: \"btn-primary w-full disabled:opacity-50 disabled:cursor-not-allowed\",\n                                                    children: isProcessing ? 'Processing...' : 'Complete Setup'\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                                    lineNumber: 253,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                            lineNumber: 222,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                                    lineNumber: 212,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, step, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                            lineNumber: 109,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                    lineNumber: 73,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n                lineNumber: 72,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/record/page.tsx\",\n        lineNumber: 55,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/record/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/AudioRecorder.tsx":
/*!******************************************!*\
  !*** ./src/components/AudioRecorder.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AudioRecorder)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_Download_Mic_Pause_Play_Square_Trash2_Upload_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Mic,Pause,Play,Square,Trash2,Upload,Volume2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/mic.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Mic_Pause_Play_Square_Trash2_Upload_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Mic,Pause,Play,Square,Trash2,Upload,Volume2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/square.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Mic_Pause_Play_Square_Trash2_Upload_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Mic,Pause,Play,Square,Trash2,Upload,Volume2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Mic_Pause_Play_Square_Trash2_Upload_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Mic,Pause,Play,Square,Trash2,Upload,Volume2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/pause.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Mic_Pause_Play_Square_Trash2_Upload_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Mic,Pause,Play,Square,Trash2,Upload,Volume2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Mic_Pause_Play_Square_Trash2_Upload_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Mic,Pause,Play,Square,Trash2,Upload,Volume2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/volume-2.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Mic_Pause_Play_Square_Trash2_Upload_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Mic,Pause,Play,Square,Trash2,Upload,Volume2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Mic_Pause_Play_Square_Trash2_Upload_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Mic,Pause,Play,Square,Trash2,Upload,Volume2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _lib_audio__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/audio */ \"(ssr)/./src/lib/audio.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction AudioRecorder({ onRecordingComplete, onRecordingStart, onRecordingStop, maxDuration = 120, className = '' }) {\n    const [isRecording, setIsRecording] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isPlaying, setIsPlaying] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [audioBlob, setAudioBlob] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [audioUrl, setAudioUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [duration, setDuration] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [audioLevel, setAudioLevel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isInitialized, setIsInitialized] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [permissionStatus, setPermissionStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('unknown');\n    const recorderRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const audioRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const intervalRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const animationRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AudioRecorder.useEffect\": ()=>{\n            // Check initial permission status and auto-initialize if granted\n            const initializeIfPermitted = {\n                \"AudioRecorder.useEffect.initializeIfPermitted\": async ()=>{\n                    await checkMicrophonePermission();\n                    // If permission is already granted, auto-initialize\n                    if (permissionStatus === 'granted') {\n                        await initializeRecorder();\n                    }\n                }\n            }[\"AudioRecorder.useEffect.initializeIfPermitted\"];\n            initializeIfPermitted();\n            return ({\n                \"AudioRecorder.useEffect\": ()=>{\n                    cleanup();\n                }\n            })[\"AudioRecorder.useEffect\"];\n        }\n    }[\"AudioRecorder.useEffect\"], []);\n    // Auto-initialize when permission status changes to granted\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AudioRecorder.useEffect\": ()=>{\n            if (permissionStatus === 'granted' && !isInitialized) {\n                initializeRecorder();\n            }\n        }\n    }[\"AudioRecorder.useEffect\"], [\n        permissionStatus,\n        isInitialized\n    ]);\n    const checkMicrophonePermission = async ()=>{\n        try {\n            if (navigator.permissions) {\n                const permission = await navigator.permissions.query({\n                    name: 'microphone'\n                });\n                setPermissionStatus(permission.state);\n                permission.onchange = ()=>{\n                    setPermissionStatus(permission.state);\n                };\n            } else {\n                // Fallback: try to access microphone to check permission\n                try {\n                    const stream = await navigator.mediaDevices.getUserMedia({\n                        audio: true\n                    });\n                    stream.getTracks().forEach((track)=>track.stop());\n                    setPermissionStatus('granted');\n                } catch (err) {\n                    setPermissionStatus('denied');\n                }\n            }\n        } catch (err) {\n            console.log('Permission check failed:', err);\n            setPermissionStatus('unknown');\n        }\n    };\n    const requestMicrophonePermission = async ()=>{\n        try {\n            setError(null);\n            console.log('Requesting microphone permission...');\n            const stream = await navigator.mediaDevices.getUserMedia({\n                audio: {\n                    sampleRate: 44100,\n                    channelCount: 1,\n                    echoCancellation: true,\n                    noiseSuppression: true,\n                    autoGainControl: true\n                }\n            });\n            // Stop the test stream immediately\n            stream.getTracks().forEach((track)=>track.stop());\n            setPermissionStatus('granted');\n            setError(null);\n            // Auto-initialize after permission granted\n            await initializeRecorder();\n        } catch (err) {\n            console.error('Permission request failed:', err);\n            setPermissionStatus('denied');\n            if (err instanceof Error) {\n                if (err.name === 'NotAllowedError') {\n                    setError('Microphone access denied. Please click the microphone icon in your browser\\'s address bar and allow access.');\n                } else if (err.name === 'NotFoundError') {\n                    setError('No microphone found. Please connect a microphone and try again.');\n                } else {\n                    setError(`Microphone error: ${err.message}`);\n                }\n            }\n        }\n    };\n    const cleanup = ()=>{\n        if (intervalRef.current) {\n            clearInterval(intervalRef.current);\n        }\n        if (animationRef.current) {\n            cancelAnimationFrame(animationRef.current);\n        }\n        if (recorderRef.current) {\n            recorderRef.current.cleanup();\n        }\n        if (audioUrl) {\n            URL.revokeObjectURL(audioUrl);\n        }\n    };\n    const initializeRecorder = async ()=>{\n        try {\n            setError(null);\n            // Check if browser supports required APIs\n            if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {\n                throw new Error('Your browser does not support audio recording');\n            }\n            // Request microphone permission explicitly\n            console.log('Requesting microphone permission...');\n            const stream = await navigator.mediaDevices.getUserMedia({\n                audio: {\n                    sampleRate: 44100,\n                    channelCount: 1,\n                    echoCancellation: true,\n                    noiseSuppression: true,\n                    autoGainControl: true\n                }\n            });\n            // Stop the test stream\n            stream.getTracks().forEach((track)=>track.stop());\n            // Now initialize the recorder\n            recorderRef.current = new _lib_audio__WEBPACK_IMPORTED_MODULE_2__.AudioRecorder();\n            await recorderRef.current.initialize();\n            setIsInitialized(true);\n            console.log('Audio recorder initialized successfully');\n        } catch (err) {\n            console.error('Recorder initialization failed:', err);\n            if (err instanceof Error) {\n                if (err.name === 'NotAllowedError') {\n                    setError('Microphone access denied. Please allow microphone permissions and refresh the page.');\n                } else if (err.name === 'NotFoundError') {\n                    setError('No microphone found. Please connect a microphone and try again.');\n                } else if (err.name === 'NotSupportedError') {\n                    setError('Your browser does not support audio recording.');\n                } else {\n                    setError(`Microphone error: ${err.message}`);\n                }\n            } else {\n                setError('Failed to access microphone. Please check permissions.');\n            }\n        }\n    };\n    const startRecording = async ()=>{\n        try {\n            setError(null);\n            if (!recorderRef.current || !isInitialized) {\n                console.log('Initializing recorder...');\n                await initializeRecorder();\n            }\n            if (!recorderRef.current || !isInitialized) {\n                setError('Failed to initialize microphone. Please check permissions and try again.');\n                return;\n            }\n            console.log('Starting recording...');\n            setDuration(0);\n            recorderRef.current.startRecording();\n            setIsRecording(true);\n            onRecordingStart?.();\n            // Start duration timer\n            intervalRef.current = setInterval(()=>{\n                setDuration((prev)=>{\n                    const newDuration = prev + 1;\n                    if (newDuration >= maxDuration) {\n                        stopRecording();\n                    }\n                    return newDuration;\n                });\n            }, 1000);\n            // Start audio level monitoring\n            const updateAudioLevel = ()=>{\n                if (recorderRef.current && isRecording) {\n                    setAudioLevel(recorderRef.current.getAudioLevel());\n                    animationRef.current = requestAnimationFrame(updateAudioLevel);\n                }\n            };\n            updateAudioLevel();\n        } catch (err) {\n            console.error('Recording start failed:', err);\n            setError('Failed to start recording. Please check microphone permissions.');\n        }\n    };\n    const stopRecording = async ()=>{\n        if (!recorderRef.current || !isRecording) return;\n        try {\n            const blob = await recorderRef.current.stopRecording();\n            setIsRecording(false);\n            setAudioLevel(0);\n            onRecordingStop?.();\n            if (intervalRef.current) {\n                clearInterval(intervalRef.current);\n            }\n            if (animationRef.current) {\n                cancelAnimationFrame(animationRef.current);\n            }\n            // Clean up previous audio URL\n            if (audioUrl) {\n                URL.revokeObjectURL(audioUrl);\n            }\n            const newAudioUrl = _lib_audio__WEBPACK_IMPORTED_MODULE_2__.audioUtils.createAudioURL(blob);\n            setAudioBlob(blob);\n            setAudioUrl(newAudioUrl);\n            onRecordingComplete?.(blob);\n        } catch (err) {\n            setError('Failed to stop recording');\n            console.error('Recording stop failed:', err);\n        }\n    };\n    const playAudio = ()=>{\n        if (!audioRef.current || !audioUrl) return;\n        if (isPlaying) {\n            audioRef.current.pause();\n            setIsPlaying(false);\n        } else {\n            audioRef.current.play();\n            setIsPlaying(true);\n        }\n    };\n    const downloadAudio = ()=>{\n        if (!audioBlob) return;\n        _lib_audio__WEBPACK_IMPORTED_MODULE_2__.audioUtils.downloadAudio(audioBlob, `voice-recording-${Date.now()}.webm`);\n    };\n    const deleteRecording = ()=>{\n        if (audioUrl) {\n            URL.revokeObjectURL(audioUrl);\n        }\n        setAudioBlob(null);\n        setAudioUrl(null);\n        setDuration(0);\n        setIsPlaying(false);\n    };\n    const handleFileUpload = (event)=>{\n        const file = event.target.files?.[0];\n        if (!file) return;\n        const validation = _lib_audio__WEBPACK_IMPORTED_MODULE_2__.audioUtils.validateAudioFile(file);\n        if (!validation.valid) {\n            setError(validation.error || 'Invalid audio file');\n            return;\n        }\n        setError(null);\n        const blob = new Blob([\n            file\n        ], {\n            type: file.type\n        });\n        const url = _lib_audio__WEBPACK_IMPORTED_MODULE_2__.audioUtils.createAudioURL(blob);\n        if (audioUrl) {\n            URL.revokeObjectURL(audioUrl);\n        }\n        setAudioBlob(blob);\n        setAudioUrl(url);\n        onRecordingComplete?.(blob);\n    };\n    const formatTime = (seconds)=>{\n        const mins = Math.floor(seconds / 60);\n        const secs = seconds % 60;\n        return `${mins}:${secs.toString().padStart(2, '0')}`;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `space-y-6 ${className}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.AnimatePresence, {\n                children: error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: -10\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    exit: {\n                        opacity: 0,\n                        y: -10\n                    },\n                    className: \"bg-red-900/50 border border-red-700 rounded-lg p-4 text-red-200\",\n                    children: error\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                    lineNumber: 327,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                lineNumber: 325,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"card\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold mb-2\",\n                                children: \"Record Your Voice\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                lineNumber: 341,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400 mb-2\",\n                                children: [\n                                    \"Record at least 30 seconds for best results (max \",\n                                    maxDuration,\n                                    \"s)\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                lineNumber: 342,\n                                columnNumber: 11\n                            }, this),\n                            permissionStatus === 'unknown' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-blue-300\",\n                                children: \"\\uD83D\\uDCA1 On macOS: Make sure to allow microphone access in System Preferences → Security & Privacy → Microphone\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                lineNumber: 346,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                        lineNumber: 340,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-center mb-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"audio-visualizer\",\n                            children: [\n                                ...Array(12)\n                            ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"audio-bar w-3\",\n                                    style: {\n                                        height: isRecording ? `${Math.max(4, audioLevel * 60 + Math.random() * 20)}px` : '4px'\n                                    }\n                                }, i, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                    lineNumber: 356,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                            lineNumber: 354,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                        lineNumber: 353,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-2xl font-mono text-purple-400\",\n                                children: formatTime(duration)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                lineNumber: 371,\n                                columnNumber: 11\n                            }, this),\n                            maxDuration && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-gray-400\",\n                                children: [\n                                    \"/ \",\n                                    formatTime(maxDuration)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                lineNumber: 375,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                        lineNumber: 370,\n                        columnNumber: 9\n                    }, this),\n                    permissionStatus === 'denied' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6 p-4 bg-yellow-900/50 border border-yellow-700 rounded-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2 text-yellow-200 mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Mic_Pause_Play_Square_Trash2_Upload_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                        lineNumber: 385,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: \"Microphone Permission Required\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                        lineNumber: 386,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                lineNumber: 384,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-yellow-300 mb-3\",\n                                children: \"Please allow microphone access to record your voice. Click the button below to request permission.\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                lineNumber: 388,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: requestMicrophonePermission,\n                                className: \"btn-primary flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Mic_Pause_Play_Square_Trash2_Upload_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                        lineNumber: 395,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Allow Microphone Access\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                        lineNumber: 396,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                lineNumber: 391,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                        lineNumber: 383,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-center space-x-4 mb-6\",\n                        children: [\n                            (permissionStatus === 'unknown' || !isInitialized) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: requestMicrophonePermission,\n                                className: \"btn-primary flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Mic_Pause_Play_Square_Trash2_Upload_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                        lineNumber: 408,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Enable Microphone\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                        lineNumber: 409,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                lineNumber: 404,\n                                columnNumber: 13\n                            }, this),\n                            permissionStatus === 'granted' && isInitialized && !isRecording && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: startRecording,\n                                className: \"btn-primary flex items-center space-x-2\",\n                                disabled: isRecording,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Mic_Pause_Play_Square_Trash2_Upload_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                        lineNumber: 419,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Start Recording\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                        lineNumber: 420,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                lineNumber: 414,\n                                columnNumber: 13\n                            }, this),\n                            isRecording && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: stopRecording,\n                                className: \"bg-red-600 hover:bg-red-700 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-200 flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Mic_Pause_Play_Square_Trash2_Upload_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                        lineNumber: 429,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Stop Recording\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                        lineNumber: 430,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                lineNumber: 425,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"btn-secondary cursor-pointer flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Mic_Pause_Play_Square_Trash2_Upload_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                        lineNumber: 436,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Upload Audio\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                        lineNumber: 437,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"file\",\n                                        accept: \"audio/*\",\n                                        onChange: handleFileUpload,\n                                        className: \"hidden\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                        lineNumber: 438,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                lineNumber: 435,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                        lineNumber: 402,\n                        columnNumber: 9\n                    }, this),\n                    isRecording && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full bg-gray-700 rounded-full h-2 mb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gradient-to-r from-purple-600 to-blue-600 h-2 rounded-full transition-all duration-1000\",\n                            style: {\n                                width: `${duration / maxDuration * 100}%`\n                            }\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                            lineNumber: 450,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                        lineNumber: 449,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                lineNumber: 339,\n                columnNumber: 7\n            }, this),\n            audioUrl && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                initial: {\n                    opacity: 0,\n                    y: 20\n                },\n                animate: {\n                    opacity: 1,\n                    y: 0\n                },\n                className: \"card\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: playAudio,\n                                        className: \"btn-secondary flex items-center space-x-2\",\n                                        children: [\n                                            isPlaying ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Mic_Pause_Play_Square_Trash2_Upload_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                                lineNumber: 472,\n                                                columnNumber: 19\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Mic_Pause_Play_Square_Trash2_Upload_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                                lineNumber: 474,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: isPlaying ? 'Pause' : 'Play'\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                                lineNumber: 476,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                        lineNumber: 467,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2 text-gray-400\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Mic_Pause_Play_Square_Trash2_Upload_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                                lineNumber: 480,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Recording ready\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                                lineNumber: 481,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                        lineNumber: 479,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                lineNumber: 466,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: downloadAudio,\n                                        className: \"btn-secondary flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Mic_Pause_Play_Square_Trash2_Upload_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                                lineNumber: 490,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Download\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                                lineNumber: 491,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                        lineNumber: 486,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: deleteRecording,\n                                        className: \"text-red-400 hover:text-red-300 p-2 rounded-lg hover:bg-red-900/20 transition-colors\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Mic_Pause_Play_Square_Trash2_Upload_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                            lineNumber: 498,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                        lineNumber: 494,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                                lineNumber: 485,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                        lineNumber: 465,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"audio\", {\n                        ref: audioRef,\n                        src: audioUrl,\n                        onEnded: ()=>setIsPlaying(false),\n                        onPlay: ()=>setIsPlaying(true),\n                        onPause: ()=>setIsPlaying(false),\n                        className: \"hidden\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                        lineNumber: 503,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n                lineNumber: 460,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/components/AudioRecorder.tsx\",\n        lineNumber: 323,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/AudioRecorder.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/audio.ts":
/*!**************************!*\
  !*** ./src/lib/audio.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AudioRecorder: () => (/* binding */ AudioRecorder),\n/* harmony export */   audioUtils: () => (/* binding */ audioUtils)\n/* harmony export */ });\n// Audio recording and processing utilities\nclass AudioRecorder {\n    constructor(options = {}){\n        this.options = options;\n        this.mediaRecorder = null;\n        this.audioChunks = [];\n        this.stream = null;\n        this.audioContext = null;\n        this.analyser = null;\n        this.dataArray = null;\n        this.options = {\n            mimeType: 'audio/webm;codecs=opus',\n            audioBitsPerSecond: 128000,\n            sampleRate: 44100,\n            ...options\n        };\n    }\n    async initialize() {\n        try {\n            console.log('AudioRecorder: Starting initialization...');\n            // Check browser support\n            if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {\n                throw new Error('Browser does not support audio recording');\n            }\n            console.log('AudioRecorder: Requesting media stream...');\n            this.stream = await navigator.mediaDevices.getUserMedia({\n                audio: {\n                    sampleRate: this.options.sampleRate,\n                    channelCount: 1,\n                    echoCancellation: true,\n                    noiseSuppression: true,\n                    autoGainControl: true\n                }\n            });\n            console.log('AudioRecorder: Media stream obtained, setting up audio context...');\n            // Set up audio analysis\n            this.audioContext = new (window.AudioContext || window.webkitAudioContext)();\n            // Resume audio context if it's suspended (required by some browsers)\n            if (this.audioContext.state === 'suspended') {\n                await this.audioContext.resume();\n            }\n            this.analyser = this.audioContext.createAnalyser();\n            this.analyser.fftSize = 256;\n            const source = this.audioContext.createMediaStreamSource(this.stream);\n            source.connect(this.analyser);\n            this.dataArray = new Uint8Array(this.analyser.frequencyBinCount);\n            console.log('AudioRecorder: Setting up MediaRecorder...');\n            // Check MediaRecorder support and find supported mime type\n            let mimeType = this.options.mimeType;\n            if (!MediaRecorder.isTypeSupported(mimeType)) {\n                const supportedTypes = [\n                    'audio/webm;codecs=opus',\n                    'audio/webm',\n                    'audio/mp4',\n                    'audio/wav'\n                ];\n                mimeType = supportedTypes.find((type)=>MediaRecorder.isTypeSupported(type));\n                if (!mimeType) {\n                    throw new Error('No supported audio format found');\n                }\n                console.log(`AudioRecorder: Using fallback mime type: ${mimeType}`);\n            }\n            // Set up MediaRecorder\n            this.mediaRecorder = new MediaRecorder(this.stream, {\n                mimeType,\n                audioBitsPerSecond: this.options.audioBitsPerSecond\n            });\n            this.mediaRecorder.ondataavailable = (event)=>{\n                if (event.data.size > 0) {\n                    this.audioChunks.push(event.data);\n                }\n            };\n            console.log('AudioRecorder: Initialization complete!');\n        } catch (error) {\n            console.error('AudioRecorder initialization failed:', error);\n            this.cleanup();\n            throw new Error(`Failed to initialize audio recorder: ${error}`);\n        }\n    }\n    startRecording() {\n        if (!this.mediaRecorder) {\n            throw new Error('Audio recorder not initialized');\n        }\n        this.audioChunks = [];\n        this.mediaRecorder.start(100) // Collect data every 100ms\n        ;\n    }\n    stopRecording() {\n        return new Promise((resolve, reject)=>{\n            if (!this.mediaRecorder) {\n                reject(new Error('Audio recorder not initialized'));\n                return;\n            }\n            this.mediaRecorder.onstop = ()=>{\n                const audioBlob = new Blob(this.audioChunks, {\n                    type: this.options.mimeType\n                });\n                resolve(audioBlob);\n            };\n            this.mediaRecorder.stop();\n        });\n    }\n    getAudioLevel() {\n        if (!this.analyser || !this.dataArray) return 0;\n        this.analyser.getByteFrequencyData(this.dataArray);\n        let sum = 0;\n        for(let i = 0; i < this.dataArray.length; i++){\n            sum += this.dataArray[i];\n        }\n        return sum / this.dataArray.length / 255 // Normalize to 0-1\n        ;\n    }\n    isRecording() {\n        return this.mediaRecorder?.state === 'recording';\n    }\n    getAudioLevel() {\n        if (!this.analyser || !this.dataArray) return 0;\n        this.analyser.getByteFrequencyData(this.dataArray);\n        const average = this.dataArray.reduce((sum, value)=>sum + value, 0) / this.dataArray.length;\n        return average / 255 // Normalize to 0-1\n        ;\n    }\n    cleanup() {\n        if (this.mediaRecorder && this.mediaRecorder.state !== 'inactive') {\n            this.mediaRecorder.stop();\n        }\n        if (this.stream) {\n            this.stream.getTracks().forEach((track)=>track.stop());\n            this.stream = null;\n        }\n        if (this.audioContext && this.audioContext.state !== 'closed') {\n            this.audioContext.close();\n            this.audioContext = null;\n        }\n        this.mediaRecorder = null;\n        this.analyser = null;\n        this.dataArray = null;\n        this.audioChunks = [];\n    }\n}\n// Audio utility functions\nconst audioUtils = {\n    // Convert blob to base64\n    blobToBase64 (blob) {\n        return new Promise((resolve, reject)=>{\n            const reader = new FileReader();\n            reader.onload = ()=>{\n                const result = reader.result;\n                resolve(result.split(',')[1]) // Remove data:audio/webm;base64, prefix\n                ;\n            };\n            reader.onerror = reject;\n            reader.readAsDataURL(blob);\n        });\n    },\n    // Convert blob to array buffer\n    blobToArrayBuffer (blob) {\n        return new Promise((resolve, reject)=>{\n            const reader = new FileReader();\n            reader.onload = ()=>resolve(reader.result);\n            reader.onerror = reject;\n            reader.readAsArrayBuffer(blob);\n        });\n    },\n    // Create audio URL from blob\n    createAudioURL (blob) {\n        return URL.createObjectURL(blob);\n    },\n    // Download audio file\n    downloadAudio (blob, filename) {\n        const url = this.createAudioURL(blob);\n        const a = document.createElement('a');\n        a.href = url;\n        a.download = filename;\n        document.body.appendChild(a);\n        a.click();\n        document.body.removeChild(a);\n        URL.revokeObjectURL(url);\n    },\n    // Format duration in seconds to MM:SS\n    formatDuration (seconds) {\n        const minutes = Math.floor(seconds / 60);\n        const remainingSeconds = Math.floor(seconds % 60);\n        return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;\n    },\n    // Validate audio file\n    validateAudioFile (file) {\n        const maxSize = 10 * 1024 * 1024 // 10MB\n        ;\n        const allowedTypes = [\n            'audio/wav',\n            'audio/mp3',\n            'audio/mpeg',\n            'audio/webm',\n            'audio/ogg'\n        ];\n        if (file.size > maxSize) {\n            return {\n                valid: false,\n                error: 'File size must be less than 10MB'\n            };\n        }\n        if (!allowedTypes.includes(file.type)) {\n            return {\n                valid: false,\n                error: 'File must be a valid audio format (WAV, MP3, WebM, OGG)'\n            };\n        }\n        return {\n            valid: true\n        };\n    },\n    // Convert array buffer to blob\n    arrayBufferToBlob (buffer, mimeType = 'audio/mpeg') {\n        return new Blob([\n            buffer\n        ], {\n            type: mimeType\n        });\n    },\n    // Get audio duration\n    getAudioDuration (file) {\n        return new Promise((resolve, reject)=>{\n            const audio = new Audio();\n            audio.onloadedmetadata = ()=>{\n                resolve(audio.duration);\n            };\n            audio.onerror = reject;\n            audio.src = URL.createObjectURL(file);\n        });\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/audio.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/framer-motion","vendor-chunks/motion-dom","vendor-chunks/lucide-react","vendor-chunks/motion-utils","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Frecord%2Fpage&page=%2Frecord%2Fpage&appPaths=%2Frecord%2Fpage&pagePath=private-next-app-dir%2Frecord%2Fpage.tsx&appDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();