/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/voice-clone/route";
exports.ids = ["app/api/voice-clone/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive":
/*!************************************************************!*\
  !*** ./node_modules/@supabase/realtime-js/dist/main/ sync ***!
  \************************************************************/
/***/ ((module) => {

function webpackEmptyContext(req) {
	var e = new Error("Cannot find module '" + req + "'");
	e.code = 'MODULE_NOT_FOUND';
	throw e;
}
webpackEmptyContext.keys = () => ([]);
webpackEmptyContext.resolve = webpackEmptyContext;
webpackEmptyContext.id = "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive";
module.exports = webpackEmptyContext;

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fvoice-clone%2Froute&page=%2Fapi%2Fvoice-clone%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fvoice-clone%2Froute.ts&appDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fvoice-clone%2Froute&page=%2Fapi%2Fvoice-clone%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fvoice-clone%2Froute.ts&appDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Users_tahafarooqui_Documents_augment_projects_AI_Agent_voice_clone_ai_src_app_api_voice_clone_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/voice-clone/route.ts */ \"(rsc)/./src/app/api/voice-clone/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/voice-clone/route\",\n        pathname: \"/api/voice-clone\",\n        filename: \"route\",\n        bundlePath: \"app/api/voice-clone/route\"\n    },\n    resolvedPagePath: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/api/voice-clone/route.ts\",\n    nextConfigOutput,\n    userland: _Users_tahafarooqui_Documents_augment_projects_AI_Agent_voice_clone_ai_src_app_api_voice_clone_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fvoice-clone%2Froute&page=%2Fapi%2Fvoice-clone%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fvoice-clone%2Froute.ts&appDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/voice-clone/route.ts":
/*!******************************************!*\
  !*** ./src/app/api/voice-clone/route.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DELETE: () => (/* binding */ DELETE),\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_elevenlabs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/elevenlabs */ \"(rsc)/./src/lib/elevenlabs.ts\");\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/supabase */ \"(rsc)/./src/lib/supabase.ts\");\n\n\n\nasync function POST(request) {\n    try {\n        const formData = await request.formData();\n        const name = formData.get('name');\n        const description = formData.get('description');\n        const audioFile = formData.get('audio');\n        if (!name || !audioFile) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Name and audio file are required'\n            }, {\n                status: 400\n            });\n        }\n        // Validate audio file\n        const maxSize = 10 * 1024 * 1024 // 10MB\n        ;\n        if (audioFile.size > maxSize) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Audio file must be less than 10MB'\n            }, {\n                status: 400\n            });\n        }\n        const allowedTypes = [\n            'audio/wav',\n            'audio/mp3',\n            'audio/mpeg',\n            'audio/webm',\n            'audio/ogg'\n        ];\n        if (!allowedTypes.includes(audioFile.type)) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Invalid audio format. Please use WAV, MP3, WebM, or OGG'\n            }, {\n                status: 400\n            });\n        }\n        // Clone voice using ElevenLabs\n        console.log('Cloning voice with ElevenLabs...');\n        const clonedVoice = await _lib_elevenlabs__WEBPACK_IMPORTED_MODULE_1__.elevenLabsService.cloneVoice(name, description || `Voice clone: ${name}`, [\n            audioFile\n        ]);\n        // Store voice model in database\n        let voiceModel = null;\n        try {\n            voiceModel = await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.voiceModelService.create({\n                name,\n                description,\n                voice_id: clonedVoice.voice_id,\n                audio_url: ''\n            });\n        } catch (dbError) {\n            console.warn('Failed to store voice model in database:', dbError);\n            // Create a temporary voice model object for the response\n            voiceModel = {\n                id: clonedVoice.voice_id,\n                name,\n                description,\n                voice_id: clonedVoice.voice_id,\n                audio_url: '',\n                created_at: new Date().toISOString(),\n                updated_at: new Date().toISOString()\n            };\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            voiceModel,\n            elevenLabsVoice: clonedVoice\n        });\n    } catch (error) {\n        console.error('Voice cloning error:', error);\n        // Handle specific ElevenLabs errors\n        if (error instanceof Error) {\n            if (error.message.includes('quota')) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'API quota exceeded. Please try again later.'\n                }, {\n                    status: 429\n                });\n            }\n            if (error.message.includes('unauthorized')) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'Invalid API key configuration'\n                }, {\n                    status: 401\n                });\n            }\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to clone voice. Please try again.'\n        }, {\n            status: 500\n        });\n    }\n}\nasync function GET() {\n    try {\n        // Get all voice models from database\n        const voiceModels = await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.voiceModelService.getAll();\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            voiceModels\n        });\n    } catch (error) {\n        console.error('Error fetching voice models:', error);\n        // Return empty array if database is not available\n        // This allows the app to work without Supabase for demo purposes\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            voiceModels: []\n        });\n    }\n}\nasync function DELETE(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const voiceId = searchParams.get('voiceId');\n        if (!voiceId) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Voice ID is required'\n            }, {\n                status: 400\n            });\n        }\n        // Get voice model from database\n        const voiceModel = await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.voiceModelService.getById(voiceId);\n        if (!voiceModel) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Voice model not found'\n            }, {\n                status: 404\n            });\n        }\n        // Delete from ElevenLabs\n        await _lib_elevenlabs__WEBPACK_IMPORTED_MODULE_1__.elevenLabsService.deleteVoice(voiceModel.voice_id);\n        // Delete from database\n        await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.voiceModelService.delete(voiceId);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: 'Voice model deleted successfully'\n        });\n    } catch (error) {\n        console.error('Voice deletion error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to delete voice model'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2FwaS92b2ljZS1jbG9uZS9yb3V0ZS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBdUQ7QUFDSDtBQUNGO0FBRTNDLGVBQWVHLEtBQUtDLE9BQW9CO0lBQzdDLElBQUk7UUFDRixNQUFNQyxXQUFXLE1BQU1ELFFBQVFDLFFBQVE7UUFDdkMsTUFBTUMsT0FBT0QsU0FBU0UsR0FBRyxDQUFDO1FBQzFCLE1BQU1DLGNBQWNILFNBQVNFLEdBQUcsQ0FBQztRQUNqQyxNQUFNRSxZQUFZSixTQUFTRSxHQUFHLENBQUM7UUFFL0IsSUFBSSxDQUFDRCxRQUFRLENBQUNHLFdBQVc7WUFDdkIsT0FBT1QscURBQVlBLENBQUNVLElBQUksQ0FDdEI7Z0JBQUVDLE9BQU87WUFBbUMsR0FDNUM7Z0JBQUVDLFFBQVE7WUFBSTtRQUVsQjtRQUVBLHNCQUFzQjtRQUN0QixNQUFNQyxVQUFVLEtBQUssT0FBTyxLQUFLLE9BQU87O1FBQ3hDLElBQUlKLFVBQVVLLElBQUksR0FBR0QsU0FBUztZQUM1QixPQUFPYixxREFBWUEsQ0FBQ1UsSUFBSSxDQUN0QjtnQkFBRUMsT0FBTztZQUFvQyxHQUM3QztnQkFBRUMsUUFBUTtZQUFJO1FBRWxCO1FBRUEsTUFBTUcsZUFBZTtZQUFDO1lBQWE7WUFBYTtZQUFjO1lBQWM7U0FBWTtRQUN4RixJQUFJLENBQUNBLGFBQWFDLFFBQVEsQ0FBQ1AsVUFBVVEsSUFBSSxHQUFHO1lBQzFDLE9BQU9qQixxREFBWUEsQ0FBQ1UsSUFBSSxDQUN0QjtnQkFBRUMsT0FBTztZQUEwRCxHQUNuRTtnQkFBRUMsUUFBUTtZQUFJO1FBRWxCO1FBRUEsK0JBQStCO1FBQy9CTSxRQUFRQyxHQUFHLENBQUM7UUFDWixNQUFNQyxjQUFjLE1BQU1uQiw4REFBaUJBLENBQUNvQixVQUFVLENBQ3BEZixNQUNBRSxlQUFlLENBQUMsYUFBYSxFQUFFRixNQUFNLEVBQ3JDO1lBQUNHO1NBQVU7UUFHYixnQ0FBZ0M7UUFDaEMsSUFBSWEsYUFBYTtRQUNqQixJQUFJO1lBQ0ZBLGFBQWEsTUFBTXBCLDREQUFpQkEsQ0FBQ3FCLE1BQU0sQ0FBQztnQkFDMUNqQjtnQkFDQUU7Z0JBQ0FnQixVQUFVSixZQUFZSSxRQUFRO2dCQUM5QkMsV0FBVztZQUNiO1FBQ0YsRUFBRSxPQUFPQyxTQUFTO1lBQ2hCUixRQUFRUyxJQUFJLENBQUMsNENBQTRDRDtZQUN6RCx5REFBeUQ7WUFDekRKLGFBQWE7Z0JBQ1hNLElBQUlSLFlBQVlJLFFBQVE7Z0JBQ3hCbEI7Z0JBQ0FFO2dCQUNBZ0IsVUFBVUosWUFBWUksUUFBUTtnQkFDOUJDLFdBQVc7Z0JBQ1hJLFlBQVksSUFBSUMsT0FBT0MsV0FBVztnQkFDbENDLFlBQVksSUFBSUYsT0FBT0MsV0FBVztZQUNwQztRQUNGO1FBRUEsT0FBTy9CLHFEQUFZQSxDQUFDVSxJQUFJLENBQUM7WUFDdkJ1QixTQUFTO1lBQ1RYO1lBQ0FZLGlCQUFpQmQ7UUFDbkI7SUFFRixFQUFFLE9BQU9ULE9BQU87UUFDZE8sUUFBUVAsS0FBSyxDQUFDLHdCQUF3QkE7UUFFdEMsb0NBQW9DO1FBQ3BDLElBQUlBLGlCQUFpQndCLE9BQU87WUFDMUIsSUFBSXhCLE1BQU15QixPQUFPLENBQUNwQixRQUFRLENBQUMsVUFBVTtnQkFDbkMsT0FBT2hCLHFEQUFZQSxDQUFDVSxJQUFJLENBQ3RCO29CQUFFQyxPQUFPO2dCQUE4QyxHQUN2RDtvQkFBRUMsUUFBUTtnQkFBSTtZQUVsQjtZQUNBLElBQUlELE1BQU15QixPQUFPLENBQUNwQixRQUFRLENBQUMsaUJBQWlCO2dCQUMxQyxPQUFPaEIscURBQVlBLENBQUNVLElBQUksQ0FDdEI7b0JBQUVDLE9BQU87Z0JBQWdDLEdBQ3pDO29CQUFFQyxRQUFRO2dCQUFJO1lBRWxCO1FBQ0Y7UUFFQSxPQUFPWixxREFBWUEsQ0FBQ1UsSUFBSSxDQUN0QjtZQUFFQyxPQUFPO1FBQTJDLEdBQ3BEO1lBQUVDLFFBQVE7UUFBSTtJQUVsQjtBQUNGO0FBRU8sZUFBZXlCO0lBQ3BCLElBQUk7UUFDRixxQ0FBcUM7UUFDckMsTUFBTUMsY0FBYyxNQUFNcEMsNERBQWlCQSxDQUFDcUMsTUFBTTtRQUVsRCxPQUFPdkMscURBQVlBLENBQUNVLElBQUksQ0FBQztZQUN2QnVCLFNBQVM7WUFDVEs7UUFDRjtJQUNGLEVBQUUsT0FBTzNCLE9BQU87UUFDZE8sUUFBUVAsS0FBSyxDQUFDLGdDQUFnQ0E7UUFFOUMsa0RBQWtEO1FBQ2xELGlFQUFpRTtRQUNqRSxPQUFPWCxxREFBWUEsQ0FBQ1UsSUFBSSxDQUFDO1lBQ3ZCdUIsU0FBUztZQUNUSyxhQUFhLEVBQUU7UUFDakI7SUFDRjtBQUNGO0FBRU8sZUFBZUUsT0FBT3BDLE9BQW9CO0lBQy9DLElBQUk7UUFDRixNQUFNLEVBQUVxQyxZQUFZLEVBQUUsR0FBRyxJQUFJQyxJQUFJdEMsUUFBUXVDLEdBQUc7UUFDNUMsTUFBTUMsVUFBVUgsYUFBYWxDLEdBQUcsQ0FBQztRQUVqQyxJQUFJLENBQUNxQyxTQUFTO1lBQ1osT0FBTzVDLHFEQUFZQSxDQUFDVSxJQUFJLENBQ3RCO2dCQUFFQyxPQUFPO1lBQXVCLEdBQ2hDO2dCQUFFQyxRQUFRO1lBQUk7UUFFbEI7UUFFQSxnQ0FBZ0M7UUFDaEMsTUFBTVUsYUFBYSxNQUFNcEIsNERBQWlCQSxDQUFDMkMsT0FBTyxDQUFDRDtRQUNuRCxJQUFJLENBQUN0QixZQUFZO1lBQ2YsT0FBT3RCLHFEQUFZQSxDQUFDVSxJQUFJLENBQ3RCO2dCQUFFQyxPQUFPO1lBQXdCLEdBQ2pDO2dCQUFFQyxRQUFRO1lBQUk7UUFFbEI7UUFFQSx5QkFBeUI7UUFDekIsTUFBTVgsOERBQWlCQSxDQUFDNkMsV0FBVyxDQUFDeEIsV0FBV0UsUUFBUTtRQUV2RCx1QkFBdUI7UUFDdkIsTUFBTXRCLDREQUFpQkEsQ0FBQzZDLE1BQU0sQ0FBQ0g7UUFFL0IsT0FBTzVDLHFEQUFZQSxDQUFDVSxJQUFJLENBQUM7WUFDdkJ1QixTQUFTO1lBQ1RHLFNBQVM7UUFDWDtJQUVGLEVBQUUsT0FBT3pCLE9BQU87UUFDZE8sUUFBUVAsS0FBSyxDQUFDLHlCQUF5QkE7UUFDdkMsT0FBT1gscURBQVlBLENBQUNVLElBQUksQ0FDdEI7WUFBRUMsT0FBTztRQUErQixHQUN4QztZQUFFQyxRQUFRO1FBQUk7SUFFbEI7QUFDRiIsInNvdXJjZXMiOlsiL1VzZXJzL3RhaGFmYXJvb3F1aS9Eb2N1bWVudHMvYXVnbWVudC1wcm9qZWN0cy9BSS1BZ2VudC92b2ljZS1jbG9uZS1haS9zcmMvYXBwL2FwaS92b2ljZS1jbG9uZS9yb3V0ZS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBOZXh0UmVxdWVzdCwgTmV4dFJlc3BvbnNlIH0gZnJvbSAnbmV4dC9zZXJ2ZXInXG5pbXBvcnQgeyBlbGV2ZW5MYWJzU2VydmljZSB9IGZyb20gJ0AvbGliL2VsZXZlbmxhYnMnXG5pbXBvcnQgeyB2b2ljZU1vZGVsU2VydmljZSB9IGZyb20gJ0AvbGliL3N1cGFiYXNlJ1xuXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gUE9TVChyZXF1ZXN0OiBOZXh0UmVxdWVzdCkge1xuICB0cnkge1xuICAgIGNvbnN0IGZvcm1EYXRhID0gYXdhaXQgcmVxdWVzdC5mb3JtRGF0YSgpXG4gICAgY29uc3QgbmFtZSA9IGZvcm1EYXRhLmdldCgnbmFtZScpIGFzIHN0cmluZ1xuICAgIGNvbnN0IGRlc2NyaXB0aW9uID0gZm9ybURhdGEuZ2V0KCdkZXNjcmlwdGlvbicpIGFzIHN0cmluZ1xuICAgIGNvbnN0IGF1ZGlvRmlsZSA9IGZvcm1EYXRhLmdldCgnYXVkaW8nKSBhcyBGaWxlXG5cbiAgICBpZiAoIW5hbWUgfHwgIWF1ZGlvRmlsZSkge1xuICAgICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKFxuICAgICAgICB7IGVycm9yOiAnTmFtZSBhbmQgYXVkaW8gZmlsZSBhcmUgcmVxdWlyZWQnIH0sXG4gICAgICAgIHsgc3RhdHVzOiA0MDAgfVxuICAgICAgKVxuICAgIH1cblxuICAgIC8vIFZhbGlkYXRlIGF1ZGlvIGZpbGVcbiAgICBjb25zdCBtYXhTaXplID0gMTAgKiAxMDI0ICogMTAyNCAvLyAxME1CXG4gICAgaWYgKGF1ZGlvRmlsZS5zaXplID4gbWF4U2l6ZSkge1xuICAgICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKFxuICAgICAgICB7IGVycm9yOiAnQXVkaW8gZmlsZSBtdXN0IGJlIGxlc3MgdGhhbiAxME1CJyB9LFxuICAgICAgICB7IHN0YXR1czogNDAwIH1cbiAgICAgIClcbiAgICB9XG5cbiAgICBjb25zdCBhbGxvd2VkVHlwZXMgPSBbJ2F1ZGlvL3dhdicsICdhdWRpby9tcDMnLCAnYXVkaW8vbXBlZycsICdhdWRpby93ZWJtJywgJ2F1ZGlvL29nZyddXG4gICAgaWYgKCFhbGxvd2VkVHlwZXMuaW5jbHVkZXMoYXVkaW9GaWxlLnR5cGUpKSB7XG4gICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oXG4gICAgICAgIHsgZXJyb3I6ICdJbnZhbGlkIGF1ZGlvIGZvcm1hdC4gUGxlYXNlIHVzZSBXQVYsIE1QMywgV2ViTSwgb3IgT0dHJyB9LFxuICAgICAgICB7IHN0YXR1czogNDAwIH1cbiAgICAgIClcbiAgICB9XG5cbiAgICAvLyBDbG9uZSB2b2ljZSB1c2luZyBFbGV2ZW5MYWJzXG4gICAgY29uc29sZS5sb2coJ0Nsb25pbmcgdm9pY2Ugd2l0aCBFbGV2ZW5MYWJzLi4uJylcbiAgICBjb25zdCBjbG9uZWRWb2ljZSA9IGF3YWl0IGVsZXZlbkxhYnNTZXJ2aWNlLmNsb25lVm9pY2UoXG4gICAgICBuYW1lLFxuICAgICAgZGVzY3JpcHRpb24gfHwgYFZvaWNlIGNsb25lOiAke25hbWV9YCxcbiAgICAgIFthdWRpb0ZpbGVdXG4gICAgKVxuXG4gICAgLy8gU3RvcmUgdm9pY2UgbW9kZWwgaW4gZGF0YWJhc2VcbiAgICBsZXQgdm9pY2VNb2RlbCA9IG51bGxcbiAgICB0cnkge1xuICAgICAgdm9pY2VNb2RlbCA9IGF3YWl0IHZvaWNlTW9kZWxTZXJ2aWNlLmNyZWF0ZSh7XG4gICAgICAgIG5hbWUsXG4gICAgICAgIGRlc2NyaXB0aW9uLFxuICAgICAgICB2b2ljZV9pZDogY2xvbmVkVm9pY2Uudm9pY2VfaWQsXG4gICAgICAgIGF1ZGlvX3VybDogJycsIC8vIFdlJ2xsIHN0b3JlIHRoZSBvcmlnaW5hbCBhdWRpbyBVUkwgaWYgbmVlZGVkXG4gICAgICB9KVxuICAgIH0gY2F0Y2ggKGRiRXJyb3IpIHtcbiAgICAgIGNvbnNvbGUud2FybignRmFpbGVkIHRvIHN0b3JlIHZvaWNlIG1vZGVsIGluIGRhdGFiYXNlOicsIGRiRXJyb3IpXG4gICAgICAvLyBDcmVhdGUgYSB0ZW1wb3Jhcnkgdm9pY2UgbW9kZWwgb2JqZWN0IGZvciB0aGUgcmVzcG9uc2VcbiAgICAgIHZvaWNlTW9kZWwgPSB7XG4gICAgICAgIGlkOiBjbG9uZWRWb2ljZS52b2ljZV9pZCwgLy8gVXNlIEVsZXZlbkxhYnMgdm9pY2VfaWQgYXMgZmFsbGJhY2tcbiAgICAgICAgbmFtZSxcbiAgICAgICAgZGVzY3JpcHRpb24sXG4gICAgICAgIHZvaWNlX2lkOiBjbG9uZWRWb2ljZS52b2ljZV9pZCxcbiAgICAgICAgYXVkaW9fdXJsOiAnJyxcbiAgICAgICAgY3JlYXRlZF9hdDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpLFxuICAgICAgICB1cGRhdGVkX2F0OiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKClcbiAgICAgIH1cbiAgICB9XG5cbiAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oe1xuICAgICAgc3VjY2VzczogdHJ1ZSxcbiAgICAgIHZvaWNlTW9kZWwsXG4gICAgICBlbGV2ZW5MYWJzVm9pY2U6IGNsb25lZFZvaWNlXG4gICAgfSlcblxuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ1ZvaWNlIGNsb25pbmcgZXJyb3I6JywgZXJyb3IpXG4gICAgXG4gICAgLy8gSGFuZGxlIHNwZWNpZmljIEVsZXZlbkxhYnMgZXJyb3JzXG4gICAgaWYgKGVycm9yIGluc3RhbmNlb2YgRXJyb3IpIHtcbiAgICAgIGlmIChlcnJvci5tZXNzYWdlLmluY2x1ZGVzKCdxdW90YScpKSB7XG4gICAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbihcbiAgICAgICAgICB7IGVycm9yOiAnQVBJIHF1b3RhIGV4Y2VlZGVkLiBQbGVhc2UgdHJ5IGFnYWluIGxhdGVyLicgfSxcbiAgICAgICAgICB7IHN0YXR1czogNDI5IH1cbiAgICAgICAgKVxuICAgICAgfVxuICAgICAgaWYgKGVycm9yLm1lc3NhZ2UuaW5jbHVkZXMoJ3VuYXV0aG9yaXplZCcpKSB7XG4gICAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbihcbiAgICAgICAgICB7IGVycm9yOiAnSW52YWxpZCBBUEkga2V5IGNvbmZpZ3VyYXRpb24nIH0sXG4gICAgICAgICAgeyBzdGF0dXM6IDQwMSB9XG4gICAgICAgIClcbiAgICAgIH1cbiAgICB9XG5cbiAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oXG4gICAgICB7IGVycm9yOiAnRmFpbGVkIHRvIGNsb25lIHZvaWNlLiBQbGVhc2UgdHJ5IGFnYWluLicgfSxcbiAgICAgIHsgc3RhdHVzOiA1MDAgfVxuICAgIClcbiAgfVxufVxuXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gR0VUKCkge1xuICB0cnkge1xuICAgIC8vIEdldCBhbGwgdm9pY2UgbW9kZWxzIGZyb20gZGF0YWJhc2VcbiAgICBjb25zdCB2b2ljZU1vZGVscyA9IGF3YWl0IHZvaWNlTW9kZWxTZXJ2aWNlLmdldEFsbCgpXG5cbiAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oe1xuICAgICAgc3VjY2VzczogdHJ1ZSxcbiAgICAgIHZvaWNlTW9kZWxzXG4gICAgfSlcbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBjb25zb2xlLmVycm9yKCdFcnJvciBmZXRjaGluZyB2b2ljZSBtb2RlbHM6JywgZXJyb3IpXG5cbiAgICAvLyBSZXR1cm4gZW1wdHkgYXJyYXkgaWYgZGF0YWJhc2UgaXMgbm90IGF2YWlsYWJsZVxuICAgIC8vIFRoaXMgYWxsb3dzIHRoZSBhcHAgdG8gd29yayB3aXRob3V0IFN1cGFiYXNlIGZvciBkZW1vIHB1cnBvc2VzXG4gICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKHtcbiAgICAgIHN1Y2Nlc3M6IHRydWUsXG4gICAgICB2b2ljZU1vZGVsczogW11cbiAgICB9KVxuICB9XG59XG5cbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBERUxFVEUocmVxdWVzdDogTmV4dFJlcXVlc3QpIHtcbiAgdHJ5IHtcbiAgICBjb25zdCB7IHNlYXJjaFBhcmFtcyB9ID0gbmV3IFVSTChyZXF1ZXN0LnVybClcbiAgICBjb25zdCB2b2ljZUlkID0gc2VhcmNoUGFyYW1zLmdldCgndm9pY2VJZCcpXG5cbiAgICBpZiAoIXZvaWNlSWQpIHtcbiAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbihcbiAgICAgICAgeyBlcnJvcjogJ1ZvaWNlIElEIGlzIHJlcXVpcmVkJyB9LFxuICAgICAgICB7IHN0YXR1czogNDAwIH1cbiAgICAgIClcbiAgICB9XG5cbiAgICAvLyBHZXQgdm9pY2UgbW9kZWwgZnJvbSBkYXRhYmFzZVxuICAgIGNvbnN0IHZvaWNlTW9kZWwgPSBhd2FpdCB2b2ljZU1vZGVsU2VydmljZS5nZXRCeUlkKHZvaWNlSWQpXG4gICAgaWYgKCF2b2ljZU1vZGVsKSB7XG4gICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oXG4gICAgICAgIHsgZXJyb3I6ICdWb2ljZSBtb2RlbCBub3QgZm91bmQnIH0sXG4gICAgICAgIHsgc3RhdHVzOiA0MDQgfVxuICAgICAgKVxuICAgIH1cblxuICAgIC8vIERlbGV0ZSBmcm9tIEVsZXZlbkxhYnNcbiAgICBhd2FpdCBlbGV2ZW5MYWJzU2VydmljZS5kZWxldGVWb2ljZSh2b2ljZU1vZGVsLnZvaWNlX2lkKVxuXG4gICAgLy8gRGVsZXRlIGZyb20gZGF0YWJhc2VcbiAgICBhd2FpdCB2b2ljZU1vZGVsU2VydmljZS5kZWxldGUodm9pY2VJZClcblxuICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbih7XG4gICAgICBzdWNjZXNzOiB0cnVlLFxuICAgICAgbWVzc2FnZTogJ1ZvaWNlIG1vZGVsIGRlbGV0ZWQgc3VjY2Vzc2Z1bGx5J1xuICAgIH0pXG5cbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBjb25zb2xlLmVycm9yKCdWb2ljZSBkZWxldGlvbiBlcnJvcjonLCBlcnJvcilcbiAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oXG4gICAgICB7IGVycm9yOiAnRmFpbGVkIHRvIGRlbGV0ZSB2b2ljZSBtb2RlbCcgfSxcbiAgICAgIHsgc3RhdHVzOiA1MDAgfVxuICAgIClcbiAgfVxufVxuIl0sIm5hbWVzIjpbIk5leHRSZXNwb25zZSIsImVsZXZlbkxhYnNTZXJ2aWNlIiwidm9pY2VNb2RlbFNlcnZpY2UiLCJQT1NUIiwicmVxdWVzdCIsImZvcm1EYXRhIiwibmFtZSIsImdldCIsImRlc2NyaXB0aW9uIiwiYXVkaW9GaWxlIiwianNvbiIsImVycm9yIiwic3RhdHVzIiwibWF4U2l6ZSIsInNpemUiLCJhbGxvd2VkVHlwZXMiLCJpbmNsdWRlcyIsInR5cGUiLCJjb25zb2xlIiwibG9nIiwiY2xvbmVkVm9pY2UiLCJjbG9uZVZvaWNlIiwidm9pY2VNb2RlbCIsImNyZWF0ZSIsInZvaWNlX2lkIiwiYXVkaW9fdXJsIiwiZGJFcnJvciIsIndhcm4iLCJpZCIsImNyZWF0ZWRfYXQiLCJEYXRlIiwidG9JU09TdHJpbmciLCJ1cGRhdGVkX2F0Iiwic3VjY2VzcyIsImVsZXZlbkxhYnNWb2ljZSIsIkVycm9yIiwibWVzc2FnZSIsIkdFVCIsInZvaWNlTW9kZWxzIiwiZ2V0QWxsIiwiREVMRVRFIiwic2VhcmNoUGFyYW1zIiwiVVJMIiwidXJsIiwidm9pY2VJZCIsImdldEJ5SWQiLCJkZWxldGVWb2ljZSIsImRlbGV0ZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/voice-clone/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/elevenlabs.ts":
/*!*******************************!*\
  !*** ./src/lib/elevenlabs.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ElevenLabsService: () => (/* binding */ ElevenLabsService),\n/* harmony export */   elevenLabsService: () => (/* binding */ elevenLabsService)\n/* harmony export */ });\n// ElevenLabs API integration\nconst ELEVENLABS_API_KEY = \"***************************************************\";\nconst ELEVENLABS_BASE_URL = 'https://api.elevenlabs.io/v1';\nclass ElevenLabsService {\n    constructor(apiKey){\n        this.apiKey = apiKey || ELEVENLABS_API_KEY || '';\n        if (!this.apiKey) {\n            throw new Error('ElevenLabs API key is required');\n        }\n    }\n    async makeRequest(endpoint, options = {}) {\n        const url = `${ELEVENLABS_BASE_URL}${endpoint}`;\n        const response = await fetch(url, {\n            ...options,\n            headers: {\n                'xi-api-key': this.apiKey,\n                'Content-Type': 'application/json',\n                ...options.headers\n            }\n        });\n        if (!response.ok) {\n            const error = await response.text();\n            throw new Error(`ElevenLabs API error: ${response.status} - ${error}`);\n        }\n        return response;\n    }\n    async getVoices() {\n        const response = await this.makeRequest('/voices');\n        return response.json();\n    }\n    async cloneVoice(name, description, files) {\n        const formData = new FormData();\n        formData.append('name', name);\n        formData.append('description', description);\n        files.forEach((file, index)=>{\n            formData.append('files', file, `sample_${index}.${file.name.split('.').pop()}`);\n        });\n        const response = await fetch(`${ELEVENLABS_BASE_URL}/voices/add`, {\n            method: 'POST',\n            headers: {\n                'xi-api-key': this.apiKey\n            },\n            body: formData\n        });\n        if (!response.ok) {\n            const error = await response.text();\n            throw new Error(`Voice cloning failed: ${response.status} - ${error}`);\n        }\n        return response.json();\n    }\n    async generateSpeech(voiceId, text, options = {}) {\n        const body = {\n            text,\n            model_id: options.model_id || 'eleven_monolingual_v1',\n            voice_settings: {\n                stability: 0.5,\n                similarity_boost: 0.5,\n                style: 0.0,\n                use_speaker_boost: true,\n                ...options.voice_settings\n            }\n        };\n        const response = await fetch(`${ELEVENLABS_BASE_URL}/text-to-speech/${voiceId}`, {\n            method: 'POST',\n            headers: {\n                'xi-api-key': this.apiKey,\n                'Content-Type': 'application/json',\n                'Accept': 'audio/mpeg'\n            },\n            body: JSON.stringify(body)\n        });\n        if (!response.ok) {\n            const error = await response.text();\n            throw new Error(`Speech generation failed: ${response.status} - ${error}`);\n        }\n        return response.arrayBuffer();\n    }\n    async deleteVoice(voiceId) {\n        await this.makeRequest(`/voices/${voiceId}`, {\n            method: 'DELETE'\n        });\n    }\n    async getVoiceSettings(voiceId) {\n        const response = await this.makeRequest(`/voices/${voiceId}/settings`);\n        return response.json();\n    }\n    async updateVoiceSettings(voiceId, settings) {\n        const response = await this.makeRequest(`/voices/${voiceId}/settings/edit`, {\n            method: 'POST',\n            body: JSON.stringify(settings)\n        });\n        return response.json();\n    }\n}\nconst elevenLabsService = new ElevenLabsService();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/elevenlabs.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/supabase.ts":
/*!*****************************!*\
  !*** ./src/lib/supabase.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   generatedAudioService: () => (/* binding */ generatedAudioService),\n/* harmony export */   supabase: () => (/* binding */ supabase),\n/* harmony export */   voiceModelService: () => (/* binding */ voiceModelService)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n\nconst supabaseUrl = \"https://spybbgwbirxkufxkpgbi.supabase.co\";\nconst supabaseAnonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNweWJiZ3diaXJ4a3VmeGtwZ2JpIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDkwNTU0NDksImV4cCI6MjA2NDYzMTQ0OX0.VUtf3Dtdzzuc5Tw379MnMgpeSmH4o6ikp4jYkiNvV0c\";\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseAnonKey);\n// Database operations\nconst voiceModelService = {\n    async create (voiceModel) {\n        const { data, error } = await supabase.from('voice_models').insert(voiceModel).select().single();\n        if (error) throw error;\n        return data;\n    },\n    async getAll () {\n        const { data, error } = await supabase.from('voice_models').select('*').order('created_at', {\n            ascending: false\n        });\n        if (error) throw error;\n        return data;\n    },\n    async getById (id) {\n        const { data, error } = await supabase.from('voice_models').select('*').eq('id', id).single();\n        if (error) throw error;\n        return data;\n    },\n    async delete (id) {\n        const { error } = await supabase.from('voice_models').delete().eq('id', id);\n        if (error) throw error;\n    }\n};\nconst generatedAudioService = {\n    async create (audio) {\n        const { data, error } = await supabase.from('generated_audio').insert(audio).select().single();\n        if (error) throw error;\n        return data;\n    },\n    async getByVoiceModel (voiceModelId) {\n        const { data, error } = await supabase.from('generated_audio').select('*').eq('voice_model_id', voiceModelId).order('created_at', {\n            ascending: false\n        });\n        if (error) throw error;\n        return data;\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/tr46","vendor-chunks/webidl-conversions"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fvoice-clone%2Froute&page=%2Fapi%2Fvoice-clone%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fvoice-clone%2Froute.ts&appDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();