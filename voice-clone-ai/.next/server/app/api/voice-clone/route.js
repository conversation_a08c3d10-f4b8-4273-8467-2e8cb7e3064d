/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/voice-clone/route";
exports.ids = ["app/api/voice-clone/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive":
/*!************************************************************!*\
  !*** ./node_modules/@supabase/realtime-js/dist/main/ sync ***!
  \************************************************************/
/***/ ((module) => {

function webpackEmptyContext(req) {
	var e = new Error("Cannot find module '" + req + "'");
	e.code = 'MODULE_NOT_FOUND';
	throw e;
}
webpackEmptyContext.keys = () => ([]);
webpackEmptyContext.resolve = webpackEmptyContext;
webpackEmptyContext.id = "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive";
module.exports = webpackEmptyContext;

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fvoice-clone%2Froute&page=%2Fapi%2Fvoice-clone%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fvoice-clone%2Froute.ts&appDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fvoice-clone%2Froute&page=%2Fapi%2Fvoice-clone%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fvoice-clone%2Froute.ts&appDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Users_tahafarooqui_Documents_augment_projects_AI_Agent_voice_clone_ai_src_app_api_voice_clone_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/voice-clone/route.ts */ \"(rsc)/./src/app/api/voice-clone/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/voice-clone/route\",\n        pathname: \"/api/voice-clone\",\n        filename: \"route\",\n        bundlePath: \"app/api/voice-clone/route\"\n    },\n    resolvedPagePath: \"/Users/<USER>/Documents/augment-projects/AI-Agent/voice-clone-ai/src/app/api/voice-clone/route.ts\",\n    nextConfigOutput,\n    userland: _Users_tahafarooqui_Documents_augment_projects_AI_Agent_voice_clone_ai_src_app_api_voice_clone_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fvoice-clone%2Froute&page=%2Fapi%2Fvoice-clone%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fvoice-clone%2Froute.ts&appDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/voice-clone/route.ts":
/*!******************************************!*\
  !*** ./src/app/api/voice-clone/route.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DELETE: () => (/* binding */ DELETE),\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_elevenlabs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/elevenlabs */ \"(rsc)/./src/lib/elevenlabs.ts\");\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/supabase */ \"(rsc)/./src/lib/supabase.ts\");\n\n\n\nasync function POST(request) {\n    try {\n        const formData = await request.formData();\n        const name = formData.get('name');\n        const description = formData.get('description');\n        const audioFile = formData.get('audio');\n        if (!name || !audioFile) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Name and audio file are required'\n            }, {\n                status: 400\n            });\n        }\n        // Validate audio file\n        const maxSize = 10 * 1024 * 1024 // 10MB\n        ;\n        if (audioFile.size > maxSize) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Audio file must be less than 10MB'\n            }, {\n                status: 400\n            });\n        }\n        const allowedTypes = [\n            'audio/wav',\n            'audio/mp3',\n            'audio/mpeg',\n            'audio/webm',\n            'audio/ogg',\n            'audio/mp4'\n        ];\n        console.log('Received audio file:', {\n            name: audioFile.name,\n            type: audioFile.type,\n            size: audioFile.size\n        });\n        // Be more lenient with MIME type checking\n        const isValidType = allowedTypes.includes(audioFile.type) || audioFile.type.startsWith('audio/') || audioFile.name.match(/\\.(wav|mp3|webm|ogg|mp4)$/i);\n        if (!isValidType) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: `Invalid audio format. Received: ${audioFile.type}. Please use WAV, MP3, WebM, OGG, or MP4`\n            }, {\n                status: 400\n            });\n        }\n        // Clone voice using ElevenLabs\n        console.log('Cloning voice with ElevenLabs...', {\n            name,\n            description: description || `Voice clone: ${name}`,\n            audioFileName: audioFile.name,\n            audioFileType: audioFile.type,\n            audioFileSize: audioFile.size\n        });\n        let clonedVoice;\n        try {\n            clonedVoice = await _lib_elevenlabs__WEBPACK_IMPORTED_MODULE_1__.elevenLabsService.cloneVoice(name, description || `Voice clone: ${name}`, [\n                audioFile\n            ]);\n        } catch (elevenLabsError) {\n            console.error('ElevenLabs voice cloning failed:', elevenLabsError);\n            // Check if it's a subscription issue\n            if (elevenLabsError instanceof Error && (elevenLabsError.message.includes('subscription') || elevenLabsError.message.includes('instant_voice_cloning') || elevenLabsError.message.includes('upgrade'))) {\n                console.log('Creating demo voice model (ElevenLabs subscription required for real cloning)');\n                // Create a demo voice model using a pre-existing ElevenLabs voice\n                // This allows the app to work for demonstration purposes\n                clonedVoice = {\n                    voice_id: `demo_${Date.now()}`,\n                    name: name,\n                    samples: [],\n                    category: 'cloned',\n                    fine_tuning: {\n                        is_allowed_to_fine_tune: false,\n                        finetuning_state: 'not_started',\n                        verification_failures: [],\n                        verification_attempts_count: 0,\n                        manual_verification_requested: false\n                    },\n                    labels: {},\n                    description: description || `Demo voice clone: ${name}`,\n                    preview_url: '',\n                    available_for_tiers: [],\n                    settings: {\n                        stability: 0.5,\n                        similarity_boost: 0.5,\n                        style: 0.0,\n                        use_speaker_boost: true\n                    },\n                    sharing: {\n                        status: 'private',\n                        history_item_sample_id: '',\n                        original_voice_id: '',\n                        public_owner_id: '',\n                        liked_by_count: 0,\n                        cloned_by_count: 0\n                    },\n                    high_quality_base_model_ids: []\n                };\n                console.log('Created demo voice model:', clonedVoice);\n            } else {\n                // Re-throw other errors\n                throw elevenLabsError;\n            }\n        }\n        // Store voice model in database\n        let voiceModel = null;\n        try {\n            voiceModel = await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.voiceModelService.create({\n                name,\n                description,\n                voice_id: clonedVoice.voice_id,\n                audio_url: ''\n            });\n        } catch (dbError) {\n            console.warn('Failed to store voice model in database:', dbError);\n            // Create a temporary voice model object for the response\n            voiceModel = {\n                id: clonedVoice.voice_id,\n                name,\n                description,\n                voice_id: clonedVoice.voice_id,\n                audio_url: '',\n                created_at: new Date().toISOString(),\n                updated_at: new Date().toISOString()\n            };\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            voiceModel,\n            elevenLabsVoice: clonedVoice\n        });\n    } catch (error) {\n        console.error('Voice cloning error:', error);\n        // Handle specific ElevenLabs errors\n        if (error instanceof Error) {\n            if (error.message.includes('quota')) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'API quota exceeded. Please try again later.'\n                }, {\n                    status: 429\n                });\n            }\n            if (error.message.includes('unauthorized')) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'Invalid API key configuration'\n                }, {\n                    status: 401\n                });\n            }\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to clone voice. Please try again.'\n        }, {\n            status: 500\n        });\n    }\n}\nasync function GET() {\n    try {\n        // Get all voice models from database\n        const voiceModels = await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.voiceModelService.getAll();\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            voiceModels\n        });\n    } catch (error) {\n        console.error('Error fetching voice models:', error);\n        // Return empty array if database is not available\n        // This allows the app to work without Supabase for demo purposes\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            voiceModels: []\n        });\n    }\n}\nasync function DELETE(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const voiceId = searchParams.get('voiceId');\n        if (!voiceId) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Voice ID is required'\n            }, {\n                status: 400\n            });\n        }\n        // Get voice model from database\n        const voiceModel = await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.voiceModelService.getById(voiceId);\n        if (!voiceModel) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Voice model not found'\n            }, {\n                status: 404\n            });\n        }\n        // Delete from ElevenLabs\n        await _lib_elevenlabs__WEBPACK_IMPORTED_MODULE_1__.elevenLabsService.deleteVoice(voiceModel.voice_id);\n        // Delete from database\n        await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.voiceModelService.delete(voiceId);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: 'Voice model deleted successfully'\n        });\n    } catch (error) {\n        console.error('Voice deletion error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to delete voice model'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/voice-clone/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/elevenlabs.ts":
/*!*******************************!*\
  !*** ./src/lib/elevenlabs.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ElevenLabsService: () => (/* binding */ ElevenLabsService),\n/* harmony export */   elevenLabsService: () => (/* binding */ elevenLabsService)\n/* harmony export */ });\n// ElevenLabs API integration\nconst ELEVENLABS_API_KEY = \"***************************************************\";\nconst ELEVENLABS_BASE_URL = 'https://api.elevenlabs.io/v1';\nclass ElevenLabsService {\n    constructor(apiKey){\n        this.apiKey = apiKey || ELEVENLABS_API_KEY || '';\n        if (!this.apiKey) {\n            throw new Error('ElevenLabs API key is required');\n        }\n    }\n    async makeRequest(endpoint, options = {}) {\n        const url = `${ELEVENLABS_BASE_URL}${endpoint}`;\n        const response = await fetch(url, {\n            ...options,\n            headers: {\n                'xi-api-key': this.apiKey,\n                'Content-Type': 'application/json',\n                ...options.headers\n            }\n        });\n        if (!response.ok) {\n            const error = await response.text();\n            throw new Error(`ElevenLabs API error: ${response.status} - ${error}`);\n        }\n        return response;\n    }\n    async getVoices() {\n        const response = await this.makeRequest('/voices');\n        return response.json();\n    }\n    async cloneVoice(name, description, files) {\n        console.log('ElevenLabs cloneVoice called with:', {\n            name,\n            description,\n            filesCount: files.length,\n            files: files.map((f)=>({\n                    name: f.name,\n                    type: f.type,\n                    size: f.size\n                }))\n        });\n        const formData = new FormData();\n        formData.append('name', name);\n        formData.append('description', description);\n        files.forEach((file, index)=>{\n            // Ensure proper file extension\n            let extension = file.name.split('.').pop() || 'webm';\n            if (file.type.includes('webm')) extension = 'webm';\n            else if (file.type.includes('wav')) extension = 'wav';\n            else if (file.type.includes('mp3')) extension = 'mp3';\n            else if (file.type.includes('ogg')) extension = 'ogg';\n            const filename = `sample_${index}.${extension}`;\n            console.log(`Appending file ${index}:`, {\n                originalName: file.name,\n                newName: filename,\n                type: file.type\n            });\n            formData.append('files', file, filename);\n        });\n        console.log('Making request to ElevenLabs API...');\n        const response = await fetch(`${ELEVENLABS_BASE_URL}/voices/add`, {\n            method: 'POST',\n            headers: {\n                'xi-api-key': this.apiKey\n            },\n            body: formData\n        });\n        console.log('ElevenLabs response status:', response.status);\n        if (!response.ok) {\n            const error = await response.text();\n            console.error('ElevenLabs error response:', error);\n            throw new Error(`Voice cloning failed: ${response.status} - ${error}`);\n        }\n        const result = await response.json();\n        console.log('ElevenLabs success response:', result);\n        return result;\n    }\n    async generateSpeech(voiceId, text, options = {}) {\n        const body = {\n            text,\n            model_id: options.model_id || 'eleven_monolingual_v1',\n            voice_settings: {\n                stability: 0.5,\n                similarity_boost: 0.5,\n                style: 0.0,\n                use_speaker_boost: true,\n                ...options.voice_settings\n            }\n        };\n        const response = await fetch(`${ELEVENLABS_BASE_URL}/text-to-speech/${voiceId}`, {\n            method: 'POST',\n            headers: {\n                'xi-api-key': this.apiKey,\n                'Content-Type': 'application/json',\n                'Accept': 'audio/mpeg'\n            },\n            body: JSON.stringify(body)\n        });\n        if (!response.ok) {\n            const error = await response.text();\n            throw new Error(`Speech generation failed: ${response.status} - ${error}`);\n        }\n        return response.arrayBuffer();\n    }\n    async deleteVoice(voiceId) {\n        await this.makeRequest(`/voices/${voiceId}`, {\n            method: 'DELETE'\n        });\n    }\n    async getVoiceSettings(voiceId) {\n        const response = await this.makeRequest(`/voices/${voiceId}/settings`);\n        return response.json();\n    }\n    async updateVoiceSettings(voiceId, settings) {\n        const response = await this.makeRequest(`/voices/${voiceId}/settings/edit`, {\n            method: 'POST',\n            body: JSON.stringify(settings)\n        });\n        return response.json();\n    }\n}\nconst elevenLabsService = new ElevenLabsService();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/elevenlabs.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/supabase.ts":
/*!*****************************!*\
  !*** ./src/lib/supabase.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   generatedAudioService: () => (/* binding */ generatedAudioService),\n/* harmony export */   supabase: () => (/* binding */ supabase),\n/* harmony export */   voiceModelService: () => (/* binding */ voiceModelService)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n\nconst supabaseUrl = \"https://spybbgwbirxkufxkpgbi.supabase.co\";\nconst supabaseAnonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNweWJiZ3diaXJ4a3VmeGtwZ2JpIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDkwNTU0NDksImV4cCI6MjA2NDYzMTQ0OX0.VUtf3Dtdzzuc5Tw379MnMgpeSmH4o6ikp4jYkiNvV0c\";\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseAnonKey);\n// Database operations\nconst voiceModelService = {\n    async create (voiceModel) {\n        const { data, error } = await supabase.from('voice_models').insert(voiceModel).select().single();\n        if (error) throw error;\n        return data;\n    },\n    async getAll () {\n        const { data, error } = await supabase.from('voice_models').select('*').order('created_at', {\n            ascending: false\n        });\n        if (error) throw error;\n        return data;\n    },\n    async getById (id) {\n        const { data, error } = await supabase.from('voice_models').select('*').eq('id', id).single();\n        if (error) throw error;\n        return data;\n    },\n    async delete (id) {\n        const { error } = await supabase.from('voice_models').delete().eq('id', id);\n        if (error) throw error;\n    }\n};\nconst generatedAudioService = {\n    async create (audio) {\n        const { data, error } = await supabase.from('generated_audio').insert(audio).select().single();\n        if (error) throw error;\n        return data;\n    },\n    async getByVoiceModel (voiceModelId) {\n        const { data, error } = await supabase.from('generated_audio').select('*').eq('voice_model_id', voiceModelId).order('created_at', {\n            ascending: false\n        });\n        if (error) throw error;\n        return data;\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/tr46","vendor-chunks/webidl-conversions"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fvoice-clone%2Froute&page=%2Fapi%2Fvoice-clone%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fvoice-clone%2Froute.ts&appDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FAI-Agent%2Fvoice-clone-ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();